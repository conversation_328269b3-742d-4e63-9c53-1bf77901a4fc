<?php

// Test script for Canvas assignments and submissions processing
// This script helps debug the assignment processing issues

require_once(dirname(__FILE__) . '/../../vendor/autoload.php');
require_once(dirname(__FILE__) . '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');

use GuzzleHttp\Client;

// Test configuration
$config = json_decode(json_encode([
    'canvas' => [
        'baseUrl' => 'https://wayzata.instructure.com',
        'clientId' => '46400000000000260',
        'clientSecret' => 'YPyF7cmTB88PuCTKCZeCzQNYkEt7JXN8hWK27JnmJWv83UNwTtntrt3DwDzy9fKA',
        'redirectUri' => 'https://oauth.pstmn.io/v1/callback',
        'refreshToken' => '4640~yJNfc3kwvtDkwvwyf9ffeD6zrTGEvLcP7TRPR2nk2z7GFYuHax2F67WYF4RPtGfn'
    ]
]));

// Include the Canvas functions from localCanvas.php
require_once(dirname(__FILE__) . '/localCanvas.php');

echo "🧪 === CANVAS ASSIGNMENTS TEST SCRIPT ===\n\n";

// Step 1: Authenticate
echo "🔐 Step 1: Authenticating with Canvas...\n";
$accessToken = getCanvasAccessToken($config->canvas);
if (!$accessToken) {
    die("❌ Authentication failed\n");
}
echo "✅ Authentication successful\n\n";

// Step 2: Get a test course ID from your sections file
echo "📂 Step 2: Reading test data...\n";
$documentsPath = '/Users/<USER>/Documents/';
$sectionsFilePath = $documentsPath . 'canvas_section_inventory.csv';

if (!file_exists($sectionsFilePath)) {
    die("❌ Sections file not found: $sectionsFilePath\n");
}

$sectionsContent = file_get_contents($sectionsFilePath);
$sections = parseCSVFile($sectionsContent, "\r\n", ',', 2);

// Find first active section
$testCourseId = null;
$testSectionCode = null;
foreach ($sections as $section) {
    if (is_array($section) && count($section) >= 3 && strtoupper($section[1]) === 'Y') {
        $testSectionCode = $section[0];
        $testCourseId = $section[2];
        break;
    }
}

if (!$testCourseId) {
    die("❌ No active Canvas courses found in sections file\n");
}

echo "✅ Found test course: $testCourseId (Section: $testSectionCode)\n\n";

// Step 3: Test the Canvas API calls
echo "🔍 Step 3: Testing Canvas API calls for course $testCourseId...\n";

// Test assignments
echo "📝 Fetching assignments...\n";
$assignments = getCanvasAssignments($config->canvas->baseUrl, $accessToken, $testCourseId);
if ($assignments === false) {
    echo "❌ Failed to fetch assignments\n";
} else {
    echo "✅ Found " . count($assignments) . " assignments\n";
    if (count($assignments) > 0) {
        $firstAssignment = $assignments[0];
        echo "   📋 First assignment: " . ($firstAssignment['name'] ?? 'No name') . "\n";
        echo "   📊 Points possible: " . ($firstAssignment['points_possible'] ?? 'N/A') . "\n";
        echo "   📅 Due date: " . ($firstAssignment['due_at'] ?? 'No due date') . "\n";
    }
}

// Test assignment groups
echo "\n📂 Fetching assignment groups...\n";
$assignmentGroups = getCanvasAssignmentGroups($config->canvas->baseUrl, $accessToken, $testCourseId);
if ($assignmentGroups === false) {
    echo "❌ Failed to fetch assignment groups\n";
} else {
    echo "✅ Found " . count($assignmentGroups) . " assignment groups\n";
    foreach ($assignmentGroups as $group) {
        echo "   📁 Group: " . ($group['name'] ?? 'No name') . " (ID: " . ($group['id'] ?? 'N/A') . ")\n";
    }
}

// Test submissions
echo "\n📊 Fetching submissions...\n";
$submissions = getCanvasSubmissions($config->canvas->baseUrl, $accessToken, $testCourseId);
if ($submissions === false) {
    echo "❌ Failed to fetch submissions\n";
} else {
    echo "✅ Found " . count($submissions) . " submissions\n";
    
    // Analyze submissions
    $submissionsWithGrades = 0;
    $submissionsWithStudentIds = 0;
    $uniqueStudents = [];
    $uniqueAssignments = [];
    
    foreach ($submissions as $submission) {
        if (isset($submission['score']) && $submission['score'] !== null) {
            $submissionsWithGrades++;
        }
        
        $studentId = getStudentIntegrationId($submission);
        if (!empty($studentId)) {
            $submissionsWithStudentIds++;
            $uniqueStudents[$studentId] = true;
        }
        
        if (isset($submission['assignment_id'])) {
            $uniqueAssignments[$submission['assignment_id']] = true;
        }
    }
    
    echo "   📈 Submissions with grades: $submissionsWithGrades\n";
    echo "   👥 Submissions with student IDs: $submissionsWithStudentIds\n";
    echo "   🎓 Unique students: " . count($uniqueStudents) . "\n";
    echo "   📝 Unique assignments: " . count($uniqueAssignments) . "\n";
    
    // Show sample submission
    if (count($submissions) > 0) {
        $sampleSubmission = $submissions[0];
        echo "\n   📋 Sample submission:\n";
        echo "      Assignment ID: " . ($sampleSubmission['assignment_id'] ?? 'N/A') . "\n";
        echo "      User ID: " . ($sampleSubmission['user_id'] ?? 'N/A') . "\n";
        echo "      Score: " . ($sampleSubmission['score'] ?? 'N/A') . "\n";
        echo "      Grade: " . ($sampleSubmission['grade'] ?? 'N/A') . "\n";
        
        if (isset($sampleSubmission['user'])) {
            echo "      Student integration_id: " . ($sampleSubmission['user']['integration_id'] ?? 'N/A') . "\n";
            echo "      Student sis_user_id: " . ($sampleSubmission['user']['sis_user_id'] ?? 'N/A') . "\n";
            echo "      Student name: " . ($sampleSubmission['user']['name'] ?? 'N/A') . "\n";
        }
    }
}

echo "\n🎯 Step 4: Analysis Summary\n";
echo "=====================================\n";

if ($assignments !== false && $submissions !== false) {
    $assignmentCount = count($assignments);
    $submissionCount = count($submissions);
    $expectedSubmissions = $assignmentCount * count($uniqueStudents);
    
    echo "📊 Assignments found: $assignmentCount\n";
    echo "📊 Submissions found: $submissionCount\n";
    echo "📊 Expected submissions (assignments × students): $expectedSubmissions\n";
    
    if ($submissionCount < $expectedSubmissions * 0.2) {
        echo "⚠️  WARNING: Very low submission count - only " . round(($submissionCount / $expectedSubmissions) * 100, 1) . "% of expected\n";
        echo "   This could explain the 80% missing assignments issue\n";
    } else {
        echo "✅ Submission count looks reasonable\n";
    }
    
    if ($submissionsWithStudentIds < $submissionCount * 0.9) {
        echo "⚠️  WARNING: Many submissions missing student integration IDs\n";
        echo "   This could cause student_id to be empty in assignment records\n";
    } else {
        echo "✅ Most submissions have proper student IDs\n";
    }
}

echo "\n✅ Test complete!\n";
