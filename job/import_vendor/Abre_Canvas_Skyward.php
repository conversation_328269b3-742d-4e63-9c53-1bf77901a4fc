<?php

/*
* Copyright Abre.io Inc.
*/

// Enable garbage collection
if (function_exists('gc_enable')) {
    gc_enable();
}

// Check initial memory usage
$initialMemory = memory_get_usage(true) / 1024 / 1024;
if ($initialMemory > 100) {
    error_log("WARNING: High initial memory usage ({$initialMemory}MB). Consider restarting PHP for best performance.");
}

require_once(dirname(__FILE__) . '/../../vendor/autoload.php');
require_once(dirname(__FILE__) . '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');

use Google\Cloud\Storage\StorageClient;
use phpseclib\Net\SFTP;
use GuzzleHttp\Client;

/**
 * Main job function for Canvas-Skyward grades and assignments import
 */
function runJob($db, $siteID, $config)
{
    echo "Starting runJob function...\n";
    $cronName = 'Abre Canvas-Skyward';
    $currentSchoolYearID = getCurrentSchoolYearID($db);
    echo "Current School Year ID: $currentSchoolYearID\n";

    try {
        $uuid = Logger::logCronStart($db, $siteID, $cronName);

        // Define constants
        define('SAFE_COLUMN_COUNT', 12);
        define('MAX_IMPORT_LIMIT', 25);
        define('CANVAS_API_TIMEOUT', 30);

        // Initialize variables
        $error = null;
        $skip = null;
        $separator = "\r\n";

        // Authenticate with Canvas API
        echo "Authenticating with Canvas API...\n";
        $accessToken = getCanvasAccessToken($config->canvas);
        if (!$accessToken) {
            echo "Authentication failed - exiting\n";
            throw new Exception('Failed to authenticate with Canvas API');
        }
        echo "Got access token: " . substr($accessToken, 0, 20) . "...\n";

        // Get Canvas accounts and terms
        $accounts = getCanvasAccounts($config->canvas->baseUrl, $accessToken);
        if (empty($accounts)) {
            throw new Exception('No Canvas accounts found');
        }

        $accountId = $accounts[0]['id'];
        $terms = getCanvasTerms($config->canvas->baseUrl, $accessToken, $accountId);
        if (empty($terms)) {
            throw new Exception('No Canvas terms found');
        }

        // Connect to SFTP and read Skyward files
        echo "Connecting to SFTP...\n";
        $sftp = new SFTP($config->sftp->ip);
        if (!$sftp->login($config->sftp->userName, $config->sftp->password)) {
            throw new Exception("Login to SFTP failed.");
        }
        echo "SFTP connection established\n";

        $skywardData = processSkywardFiles($sftp, $separator);
        if (empty($skywardData)) {
            $skip = true;
            throw new Exception('No Skyward files found or empty files.');
        }

        // Memory check before starting
        logMemoryUsage("Script start");

        // Safety check - don't start if memory is already high
        $initialMemory = memory_get_usage(true) / 1024 / 1024;
        if ($initialMemory > 500) {
            echo "High initial memory usage detected: {$initialMemory}MB\n";
        }

        // Process Canvas data for each section
        echo "\n=== PROCESSING CANVAS GRADES ===\n";
        logMemoryUsage("Before grades processing");
        $canvasGrades = processCanvasGrades($config->canvas->baseUrl, $accessToken, $skywardData, $terms);
        echo "Canvas grades processing result: " . count($canvasGrades) . " grade records\n";
        logMemoryUsage("After grades processing");
        
        // Start database transaction
        echo "\n=== DATABASE OPERATIONS ===\n";
        echo "Starting database transaction...\n";
        $db->begin_transaction();

        try {
            // Clear existing data
            echo "Clearing existing data for site $siteID, school year $currentSchoolYearID...\n";
            $gradesDeleteResult = $db->query("DELETE FROM abre_grades WHERE site_id = $siteID AND school_year_id = $currentSchoolYearID");
            $assignmentsDeleteResult = $db->query("DELETE FROM abre_assignments WHERE site_id = $siteID AND school_year_id = $currentSchoolYearID");

            echo "Existing data cleared\n";

            // Process Canvas assignments (inserts during processing to save memory)
            echo "\n=== PROCESSING CANVAS ASSIGNMENTS ===\n";
            logMemoryUsage("Before assignments processing");
            $assignmentsProcessed = processCanvasAssignments($config->canvas->baseUrl, $accessToken, $skywardData, $terms, $db, $siteID, $currentSchoolYearID);
            echo "📊 Canvas assignments processing result: $assignmentsProcessed assignment records inserted\n";
            logMemoryUsage("After assignments processing");

            // Merge grades data (Skyward takes priority)
            echo "\n=== MERGING GRADE DATA ===\n";
            logMemoryUsage("Before grade merging");
            $mergedGrades = mergeGradeData($skywardData['grades'], $canvasGrades);
            echo "Merged grades result: " . count($mergedGrades) . " total grade records\n";
            logMemoryUsage("After grade merging");

            // Insert grades
            echo "\n=== INSERTING GRADES ===\n";
            logMemoryUsage("Before grade insertion");
            $gradesInserted = insertGradesBatch($db, $mergedGrades, $siteID, $currentSchoolYearID);
            logMemoryUsage("After grade insertion");

            // Assignments already inserted during processing
            $assignmentsInserted = $assignmentsProcessed;

            // Upload to GCS before memory cleanup
            if (isset($config->gcs) && isset($config->gcs->bucket)) {
                uploadToGCS($mergedGrades, [], $config->gcs, $siteID);
            }

            // Memory cleanup
            echo "\nPerforming memory cleanup...\n";
            logMemoryUsage("Before cleanup");
            $canvasGrades = null;
            $mergedGrades = null;
            $skywardData = null;
            $allEnrollments = null;
            $allAssignments = null;
            unset($canvasGrades, $mergedGrades, $skywardData, $allEnrollments, $allAssignments);

            // Force garbage collection
            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }
            logMemoryUsage("After cleanup");
            echo "Memory cleanup complete\n";

            // Commit transaction
            echo "\nCommitting transaction...\n";
            $db->commit();
            echo "Transaction committed successfully\n";

        } catch (Exception $e) {
            $db->rollback();
            throw new Exception('Database operation failed: ' . $e->getMessage());
        }

    } catch (Exception $ex) {
        $error = $ex->getMessage();
    }

    $details = [];
    if (isset($error) && !is_null($error)) {
        $details['error'] = $error;
        $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
    } else {
        $details = [
            'gradesInserted'      => $gradesInserted ?? 0,
            'assignmentsInserted' => $assignmentsInserted ?? 0,
            'skywardRecords'      => count($skywardData['grades'] ?? []),
            'canvasRecords'       => count($canvasGrades ?? [])
        ];
        $status = CRON_SUCCESS;
    }

    Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}

echo "=== Starting Canvas-Skyward Integration ===\n";
echo "Site ID: $siteID\n";
echo "School Year ID: $schoolYearID\n";
echo "Canvas Base URL: " . $config->canvas->baseUrl . "\n\n";

// Call the function to run the integration
runJob($db, $siteID, $config);

// Clean up old cache files
cleanupCache();

echo "\n=== Integration Complete ===\n";

/**
 * Get Canvas API access token using OAuth2 client credentials flow
 */
function getCanvasAccessToken($canvasConfig)
{
    echo "Attempting Canvas authentication...\n";
    try {
        $client = new Client([
            'timeout' => CANVAS_API_TIMEOUT,
            'verify'  => false
        ]);

        $response = $client->post($canvasConfig->baseUrl . '/login/oauth2/token', [
            'form_params' => [
                'grant_type'    => 'refresh_token',
                'client_id'     => $canvasConfig->clientId,
                'client_secret' => $canvasConfig->clientSecret,
                'refresh_token' => $canvasConfig->refreshToken
            ]
        ]);

        $data = json_decode($response->getBody(), true);
        if (isset($data['access_token'])) {
            echo "Authentication successful!\n";
            return $data['access_token'];
        } else {
            echo "No access token in response\n";
            echo "Response: " . json_encode($data) . "\n";
            return null;
        }

    } catch (Exception $e) {
        echo "Canvas API authentication failed: " . $e->getMessage() . "\n";
        return null;
    }
}

/**
 * Generic Canvas API request handler with rate limiting
 */
function makeCanvasAPIRequest($baseUrl, $accessToken, $endpoint, $params = [])
{
    // Add rate limiting to prevent API throttling
    usleep(100000);
    
    try {
        $client = new Client([
            'timeout'         => 30,
            'connect_timeout' => 10,
            'verify'          => false,
            'headers'         => [
                'Authorization' => 'Bearer ' . $accessToken,
                'Accept'        => 'application/json'
            ]
        ]);

        $url = $baseUrl . '/api/v1' . $endpoint;
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }

        // Only log API requests for debugging - comment out for production
        // echo " API Request: $url\n";
        $response = $client->get($url);

        if ($response->getStatusCode() !== 200) {
            echo "Canvas API returned HTTP {$response->getStatusCode()} for: $endpoint\n";
            $body = $response->getBody()->getContents();
            echo "Response body: $body\n";
            return false;
        }

        $data = json_decode($response->getBody(), true);
        return ['data' => $data, 'response' => $response];

    } catch (Exception $e) {
        echo "Canvas API request failed: $endpoint - " . $e->getMessage() . "\n";
        return false;
    }
}

/**
 * Get all paginated data from Canvas API
 */
function getAllPaginatedCanvasData($baseUrl, $accessToken, $endpoint, $params = [])
{
    $allData = [];
    $currentEndpoint = $endpoint;
    $currentParams = $params;
    $pageCount = 0;
    $maxPages = 10000;
    
    do {
        $pageCount++;
        echo "Fetching page $pageCount...\n";
        
        $result = makeCanvasAPIRequest($baseUrl, $accessToken, $currentEndpoint, $currentParams);
        
        if ($result === false) {
            echo "Failed to fetch page $pageCount\n";
            break;
        }
        
        $data = $result['data'];
        $response = $result['response'];
        
        if (is_array($data)) {
            $allData = array_merge($allData, $data);
            // Only log every 5th page to reduce noise
            if ($pageCount % 5 === 0 || $pageCount === 1) {
                echo "Page $pageCount: Added " . count($data) . " records (Total: " . count($allData) . ")\n";
            }
        }
        
        // Check for next page
        $linkHeader = $response->getHeader('Link');
        $nextUrl = null;
        
        if (!empty($linkHeader)) {
            $nextUrl = getNextPageUrl($linkHeader[0]);
        }
        
        if ($nextUrl) {
            // Parse the next URL to get endpoint and params
            $parsedUrl = parse_url($nextUrl);
            $currentEndpoint = str_replace('/api/v1', '', $parsedUrl['path']);
            parse_str($parsedUrl['query'] ?? '', $currentParams);
        }
        
        // Safety check to prevent infinite loops
        if ($pageCount >= $maxPages) {
            echo "Reached maximum page limit ($maxPages), stopping pagination\n";
            break;
        }
        
    } while ($nextUrl);
    
    echo "Pagination complete: $pageCount pages, " . count($allData) . " total records\n";
    return $allData;
}

/**
 * Extract next page URL from Link header
 */
function getNextPageUrl($linkHeader)
{
    $links = explode(',', $linkHeader);
    foreach ($links as $link) {
        if (strpos($link, 'rel="next"') !== false) {
            preg_match('/<(.*?)>/', $link, $matches);
            return $matches[1] ?? null;
        }
    }
    return null;
}

/**
 * Get Canvas accounts
 */
function getCanvasAccounts($baseUrl, $accessToken)
{
    $result = makeCanvasAPIRequest($baseUrl, $accessToken, '/accounts');
    return $result === false ? false : $result['data'];
}

/**
 * Get Canvas terms for an account
 */
function getCanvasTerms($baseUrl, $accessToken, $accountId)
{
    $result = makeCanvasAPIRequest($baseUrl, $accessToken, "/accounts/$accountId/terms");
    return $result === false ? false : $result['data'];
}

/**
 * Get Canvas courses for a term
 */
function getCanvasCourses($baseUrl, $accessToken, $termId)
{
    return getAllPaginatedCanvasData($baseUrl, $accessToken, '/courses', [
        'enrollment_term_id' => $termId,
        'with_enrollments'   => 'true',
        'per_page'           => 100
    ]);
}

/**
 * Get Canvas sections for a course
 */
function getCanvasSections($baseUrl, $accessToken, $courseId)
{
    return getAllPaginatedCanvasData($baseUrl, $accessToken, "/courses/$courseId/sections", [
        'per_page' => 100
    ]);
}

/**
 * Get Canvas enrollments with grades for a section
 */
function getCanvasEnrollments($baseUrl, $accessToken, $courseId)
{
    try {
        $result = getAllPaginatedCanvasData($baseUrl, $accessToken, "/courses/$courseId/enrollments", [
            'type[]'    => 'StudentEnrollment',
            'include[]' => 'grades',
            'per_page'  => 100
        ]);

        if ($result === false) {
            echo "Canvas API returned false for course $courseId\n";
            return false;
        }

        if (!is_array($result)) {
            echo "Canvas API returned non-array result for course $courseId: " . gettype($result) . "\n";
            return false;
        }

        return $result;

    } catch (Exception $e) {
        echo "Exception in getCanvasEnrollments for course $courseId: " . $e->getMessage() . "\n";
        return false;
    }
}

/**
 * Get Canvas assignments for a course
 */
function getCanvasAssignments($baseUrl, $accessToken, $courseId)
{
    return getAllPaginatedCanvasData($baseUrl, $accessToken, "/courses/$courseId/assignments", [
        'per_page' => 100
    ]);
}

/**
 * Get Canvas assignment groups for a course
 */
function getCanvasAssignmentGroups($baseUrl, $accessToken, $courseId)
{
    return getAllPaginatedCanvasData($baseUrl, $accessToken, "/courses/$courseId/assignment_groups", [
        'per_page' => 100
    ]);
}

/**
 * Get Canvas submissions for assignments
 */
function getCanvasSubmissions($baseUrl, $accessToken, $courseId, $studentIds = [])
{
    $params = [
        'student_ids[]' => 'all',
        'include[]'     => ['assignment', 'submission_comments'],
        'per_page'      => 100
    ];

    if (!empty($studentIds)) {
        $params['student_ids[]'] = implode(',', $studentIds);
    }

    return getAllPaginatedCanvasData($baseUrl, $accessToken, "/courses/$courseId/students/submissions", $params);
}

/**
 * Process Skyward files from local Documents folder (for local testing only)
 */
function processSkywardFilesLocal($separator)
{
    $data = ['grades' => [], 'sections' => [], 'term_map' => []];
    $documentsPath = '/Users/<USER>/Documents/';

    echo "Looking for files in: $documentsPath\n";

    // Read Abre_Grades_2.0.csv from local Documents folder (12 columns)
    $gradesFilePath = $documentsPath . 'Abre_Grades_2.0.csv';
    echo "Checking: Abre_Grades_2.0.csv\n";
    if (file_exists($gradesFilePath)) {
        echo "File exists\n";
        $gradesContent = file_get_contents($gradesFilePath);
        if ($gradesContent !== false) {
            $data['grades'] = parseCSVFile($gradesContent, $separator, ',', 6);
            echo "Parsed " . count($data['grades']) . " grade records\n";
        } else {
            echo "Failed to read file content\n";
        }
    } else {
        echo "File not found at: $gradesFilePath\n";
    }

    // Read canvas_term_map.csv from local Documents folder (5 columns)
    $termMapFilePath = $documentsPath . 'canvas_term_map.csv';
    echo "Checking: canvas_term_map.csv\n";
    if (file_exists($termMapFilePath)) {
        echo "File exists\n";
        $termMapContent = file_get_contents($termMapFilePath);
        if ($termMapContent !== false) {
            $data['term_map'] = parseCSVFile($termMapContent, $separator, ',', 3);
            echo "Parsed " . count($data['term_map']) . " term mapping records\n";
        } else {
            echo "Failed to read file content\n";
        }
    } else {
        echo "File not found at: $termMapFilePath\n";
    }

    // Read canvas_section_inventory.csv from local Documents folder (3 columns)
    $sectionsFilePath = $documentsPath . 'canvas_section_inventory.csv';
    echo "Checking: canvas_section_inventory.csv\n";
    if (file_exists($sectionsFilePath)) {
        echo "File exists\n";
        $sectionsContent = file_get_contents($sectionsFilePath);
        if ($sectionsContent !== false) {
            $data['sections'] = parseCSVFile($sectionsContent, $separator, ',', 2);
            echo "Parsed " . count($data['sections']) . " section records\n";
        } else {
            echo "Failed to read file content\n";
        }
    } else {
        echo "File not found at: $sectionsFilePath\n";
    }

    $sectionsWithY = 0;
    foreach ($data['sections'] as $section) {
        if (is_array($section) && count($section) >= 3 && strtoupper($section[1]) === 'Y') {
            $sectionsWithY++;
        }
    }

    echo "Summary: " . count($data['grades']) . " grades, " . count($data['sections']) . " total sections, " . $sectionsWithY . " sections exist in Canvas, " . count($data['term_map']) . " term mappings\n";

    return $data;
}

/**
 * Process Skyward files from SFTP
 */
function processSkywardFiles($sftp, $separator)
{
    $data = ['grades' => [], 'sections' => [], 'term_map' => []];

    // Read Abre_Grades_2.0.csv (updated filename)
    $gradesFile = $sftp->get('Abre_Grades_2.0.csv');
    if ($gradesFile) {
        $data['grades'] = parseCSVFile($gradesFile, $separator, ',');
    }

    // Read canvas_term_map.csv (term mapping data)
    $termMapFile = $sftp->get('canvas_term_map.csv');
    if ($termMapFile) {
        $data['term_map'] = parseCSVFile($termMapFile, $separator, ',');
    }

    // Try to find sections file - check for your specific filenames
    $sectionsFile = $sftp->get('canvas_section_inventory.csv');
    if (!$sectionsFile) {
        // Fallback to original expected name
        $sectionsFile = $sftp->get('active_year_sections.csv');
    }
    
    if ($sectionsFile) {
        $data['sections'] = parseCSVFile($sectionsFile, $separator, ',');
    }

    return $data;
}

/**
 * Parse CSV file content
 */
function parseCSVFile($fileContent, $lineSeparator, $columnSeparator, $minColumnCount = 1)
{
    $data = [];
    $lines = explode($lineSeparator, trim($fileContent));

    if (empty($lines)) {
        return $data;
    }

    // Skip header row
    array_shift($lines);

    foreach ($lines as $line) {
        if (empty(trim($line))) {
            continue;
        }

        $row = str_getcsv($line, $columnSeparator);
        if (count($row) >= $minColumnCount) {
            $data[] = $row;
        }
    }

    return $data;
}

/**
 * Process Canvas grades for sections using term mapping
 */
function processCanvasGrades($baseUrl, $accessToken, $skywardData, $terms)
{
    $canvasGrades = [];

    if (empty($skywardData['sections'])) {
        return $canvasGrades;
    }

    // Build term mapping from canvas_term_map.csv data
    $termMapping = buildTermMapping($skywardData['term_map'] ?? []);
    echo "Processing sections that exist in Canvas...\n";
    
    // Initialize caching
    $courseCache = [];
    $enrollmentCache = [];
    $cacheDir = '/tmp/canvas_cache/';
    if (!is_dir($cacheDir)) {
        mkdir($cacheDir, 0755, true);
    }
    
    // Collect all unique course IDs first
    $courseIds = [];
    foreach ($skywardData['sections'] as $section) {
        $courseId = extractSectionId($section);
        if ($courseId && !in_array($courseId, $courseIds)) {
            $courseIds[] = $courseId;
        }
    }
    
    echo "Found " . count($courseIds) . " unique courses to process\n";
    
    // Batch fetch all enrollments at once
    echo "Batch fetching all enrollments...\n";
    $allEnrollments = batchFetchEnrollments($baseUrl, $accessToken, $courseIds);
    echo "Fetched enrollments for " . count($allEnrollments) . " courses\n";
    
    // Process all sections using pre-fetched data
    echo "Processing all sections with pre-fetched data...\n";
    $processedSections = 0;
    $totalSectionsToProcess = count($skywardData['sections']);

    foreach ($skywardData['sections'] as $section) {
        $sectionId = extractSectionId($section);
        if (!$sectionId) {
            continue;
        }

        $processedSections++;
        
        // Only log every 100th section to reduce noise
        if ($processedSections % 100 === 0 || $processedSections <= 5) {
            echo "Processing section $processedSections/$totalSectionsToProcess: Course ID $sectionId\n";
        }

        // Get enrollments from batch-fetched data (no API call needed!)
        $enrollments = $allEnrollments[$sectionId] ?? [];

        // Periodic memory cleanup during enrollment processing (very frequent)
        if ($processedSections % 50 === 0) {
            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }
            logMemoryUsage("Enrollment processing cleanup after $processedSections sections");
        }

        foreach ($enrollments as $enrollment) {
            if ($enrollment['type'] === 'StudentEnrollment' && isset($enrollment['grades'])) {
                $studentId = $enrollment['user']['integration_id'] ?? $enrollment['user']['sis_user_id'];

                if ($studentId) {
                    // Get term code from mapping if available
                    $termCode = getTermCodeFromMapping($enrollment, $termMapping);
                    
                    $canvasGrades[] = [
                        'student_id'   => $studentId,
                        'section_id'   => $sectionId,
                        'course_id'    => $enrollment['course_id'] ?? $sectionId,
                        'grade'        => $enrollment['grades']['current_score'] ?? null,
                        'letter_grade' => $enrollment['grades']['current_grade'] ?? null,
                        'term_code'    => $termCode
                    ];
                }
            }
        }
    }

    echo "Processed $processedSections sections with Canvas data\n";

    // Show cache statistics
    $cacheHits = count($courseCache) + count($enrollmentCache);
    echo "Cache hits: $cacheHits, API calls saved: $cacheHits\n";

    // Memory cleanup
    $courseCache = null;
    $enrollmentCache = null;
    unset($courseCache, $enrollmentCache);

    return $canvasGrades;
}

/**
 * Build term mapping array from canvas_term_map.csv data
 */
function buildTermMapping($termMapData)
{
    $mapping = [];
    
    foreach ($termMapData as $row) {
        if (count($row) >= 5) {
            $termCode = $row[0];
            $canvasTitle = $row[1];
            $gradingPeriodId = $row[2];
            $startDate = $row[3];
            $endDate = $row[4];
            
            $mapping[$gradingPeriodId] = [
                'term_code'    => $termCode,
                'canvas_title' => $canvasTitle,
                'start_date'   => $startDate,
                'end_date'     => $endDate
            ];
        }
    }
    
    return $mapping;
}

/**
 * Get term code from Canvas enrollment using term mapping
 */
function getTermCodeFromMapping($enrollment, $termMapping)
{
    // Try to get grading period from enrollment
    if (isset($enrollment['grades']['grading_period_id'])) {
        $gradingPeriodId = $enrollment['grades']['grading_period_id'];
        if (isset($termMapping[$gradingPeriodId])) {
            return $termMapping[$gradingPeriodId]['term_code'];
        }
    }
    
    // Fallback to empty string if no mapping found
    return '';
}

/**
 * Process Canvas assignments and submissions
 */
function processCanvasAssignments($baseUrl, $accessToken, $skywardData, $terms, $db, $siteID, $currentSchoolYearID)
{
    $totalAssignmentsProcessed = 0;
    $processedCourses = 0;

    if (empty($skywardData['sections'])) {
        echo "No sections data available for assignment processing\n";
        return $totalAssignmentsProcessed;
    }

    // Initialize caching
    $assignmentCache = [];
    $cacheDir = '/tmp/canvas_cache/';
    if (!is_dir($cacheDir)) {
        mkdir($cacheDir, 0755, true);
    }

    // Collect all unique course IDs first
    $courseIds = [];
    foreach ($skywardData['sections'] as $section) {
        $courseId = extractSectionId($section);
        if ($courseId && !in_array($courseId, $courseIds)) {
            $courseIds[] = $courseId;
        }
    }

    echo "Starting assignment processing for " . count($courseIds) . " courses...\n";

    // Stream process assignments in smaller batches to prevent memory exhaustion
    echo "Stream processing assignments in small batches...\n";
    $totalCoursesToProcess = count($skywardData['sections']);

    // Create section metadata mapping from grades data to populate assignment fields
    $sectionMetadata = [];
    if (!empty($skywardData['grades'])) {
        foreach ($skywardData['grades'] as $grade) {
            if (is_array($grade) && count($grade) >= 7) {
                $sectionCode = isset($grade[2]) ? $grade[2] : '';
                if (!empty($sectionCode) && !isset($sectionMetadata[$sectionCode])) {
                    $sectionMetadata[$sectionCode] = [
                        'school_code' => isset($grade[3]) ? $grade[3] : '',
                        'staff_id'    => isset($grade[4]) ? $grade[4] : '',
                        'term_code'   => isset($grade[5]) ? $grade[5] : '',
                        'period'      => isset($grade[6]) ? $grade[6] : ''
                    ];
                }
            }
        }
        echo "Created metadata mapping for " . count($sectionMetadata) . " sections\n";
    }

    $assignmentBatch = [];
    $batchSize = 25000;
    $adaptiveBatchSize = true;

    // Process in smaller chunks of courses to avoid memory issues
    $courseBatchSize = 10;
    $courseBatches = array_chunk($courseIds, $courseBatchSize);

    foreach ($courseBatches as $batchIndex => $courseBatch) {
        echo "Processing course batch " . ($batchIndex + 1) . "/" . count($courseBatches) . " (" . count($courseBatch) . " courses)...\n";

        // Fetch assignments for this small batch only
        $batchAssignments = batchFetchAssignments($baseUrl, $accessToken, $courseBatch);
        echo "Fetched assignments for " . count($batchAssignments) . " courses in this batch\n";

        // Force garbage collection before processing this batch
        gc_collect_cycles();

        // Process sections that belong to this course batch
        foreach ($skywardData['sections'] as $section) {
        $courseId = extractSectionId($section);
        if (!$courseId || !in_array($courseId, $courseBatch)) {
            continue;
        }

        $processedCourses++;

        // Only log every 100th course
        if ($processedCourses % 100 === 0 || $processedCourses <= 5) {
            echo "Processing assignments for course $processedCourses/$totalCoursesToProcess: $courseId\n";
        }

        // Get assignments from batch-fetched data (no API call needed)
        $assignments = $batchAssignments[$courseId] ?? [];

        // Only log if assignments found and it's one of the first few or every 100th
        if (count($assignments) > 0 && ($processedCourses <= 5 || $processedCourses % 100 === 0)) {
            echo "Found " . count($assignments) . " assignments for course $courseId\n";
        }

        // Get section metadata for this course/section
        $sectionMetadataForCourse = null;
        if (!empty($sectionMetadata)) {
            // Find metadata by matching section code (courseId might be Canvas course ID, need to find matching section)
            foreach ($sectionMetadata as $sectionCode => $metadata) {
                // Try to match by courseId (which is Canvas course ID) to section data
                foreach ($skywardData['sections'] as $sectionData) {
                    if (is_array($sectionData) && count($sectionData) >= 3 && $sectionData[2] == $courseId) {
                        $skywardSectionCode = $sectionData[0];
                        if (isset($sectionMetadata[$skywardSectionCode])) {
                            $sectionMetadataForCourse = $sectionMetadata[$skywardSectionCode];
                            break 2;
                        }
                    }
                }
            }
        }

        // Add assignments to batch
        foreach ($assignments as $assignment) {
            // Get term code from mapping if available
            $mappedTermCode = '';
            if (!empty($skywardData['term_map'])) {
                $termMapping = buildTermMapping($skywardData['term_map']);
                // Try to get term code from assignment due date or course/section info
                // For now, use the term code from section metadata if available
                if ($sectionMetadataForCourse && !empty($sectionMetadataForCourse['term_code'])) {
                    $mappedTermCode = $sectionMetadataForCourse['term_code'];
                }
            }

            $assignmentBatch[] = [
                'assignment_id'    => $assignment['id'],
                'course_id'        => $courseId,
                'section_id'       => $courseId,
                'school_code'      => $sectionMetadataForCourse ? $sectionMetadataForCourse['school_code'] : '',
                'staff_id'         => $sectionMetadataForCourse ? $sectionMetadataForCourse['staff_id'] : '',
                'term_code'        => $mappedTermCode,
                'period'           => $sectionMetadataForCourse ? $sectionMetadataForCourse['period'] : '',
                'title'            => removeEmojis($assignment['name'] ?? ''),
                'description'      => substr(strip_tags(removeEmojis($assignment['description'] ?? '')), 0, 500),
                'due_date'         => $assignment['due_at'] ?? null,
                'points_possible'  => $assignment['points_possible'] ?? 0,
                'assignment_group' => $assignment['assignment_group']['name'] ?? '',
                'earned_points'    => '',
                'published'        => $assignment['published'] ? '1' : '0'
            ];

            // Insert batch when it reaches the limit (adaptive batching)
            $shouldInsertBatch = count($assignmentBatch) >= $batchSize;

            // Adaptive batching - insert early if memory usage is high
            if ($adaptiveBatchSize) {
                $memoryUsage = memory_get_usage(true) / 1024 / 1024;
                if ($memoryUsage > 3500) {
                    $shouldInsertBatch = true;
                    echo "Memory pressure detected: {$memoryUsage}MB - inserting batch early\n";
                }
            }

            if ($shouldInsertBatch) {
                logMemoryUsage("Before batch insertion");

                $currentBatchSize = count($assignmentBatch);
                $assignmentsInserted = insertAssignmentsBatch($db, $assignmentBatch, $siteID, $currentSchoolYearID);
                $totalAssignmentsProcessed += $assignmentsInserted;
                $assignmentBatch = [];

                // Force garbage collection
                if (function_exists('gc_collect_cycles')) {
                    gc_collect_cycles();
                }
                logMemoryUsage("After batch insertion and GC");
            }
        }

        // Clear assignments array to free memory
        $assignments = null;
        unset($assignments);

        // Periodic memory cleanup every 25 courses (more frequent with larger batches)
        if ($processedCourses % 25 === 0) {
            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }
            logMemoryUsage("Periodic cleanup after $processedCourses courses");
            }
        }

        // Memory cleanup after each course batch
        echo "Memory cleanup after course batch " . ($batchIndex + 1) . "...\n";
        $batchAssignments = null;
        unset($batchAssignments);
        gc_collect_cycles();
        logMemoryUsage("After course batch " . ($batchIndex + 1) . " cleanup");
    }

    // Insert any remaining assignments
    if (!empty($assignmentBatch)) {
        $assignmentsInserted = insertAssignmentsBatch($db, $assignmentBatch, $siteID, $currentSchoolYearID);
        $totalAssignmentsProcessed += $assignmentsInserted;
        echo "Inserted final batch of " . count($assignmentBatch) . " assignments\n";
    }

    echo "Assignment processing complete: $totalAssignmentsProcessed total assignments inserted\n";

    // Final memory cleanup
    $assignmentBatch = null;
    $courseBatches = null;
    unset($assignmentBatch, $courseBatches);

    // Final garbage collection
    if (function_exists('gc_collect_cycles')) {
        gc_collect_cycles();
    }
    logMemoryUsage("Final cleanup after assignment processing");

    return $totalAssignmentsProcessed;
}

/**
 * Extract section ID from Skyward canvas_section_inventory.csv
 * Format: [Section Code, Exists, Canvas Course ID]
 */
function extractSectionId($sectionData)
{
    // Handle canvas_section_inventory.csv format: [Section Code, Exists, Canvas Course ID]
    if (is_array($sectionData) && count($sectionData) >= 3) {
        $sectionCode = $sectionData[0];
        $exists = $sectionData[1];
        $canvasCourseId = $sectionData[2];

        // Only process sections that exist in Canvas
        if (strtoupper($exists) === 'Y' && !empty($canvasCourseId)) {
            // Return Canvas Course ID as primary identifier for Canvas API
            return $canvasCourseId;
        }
    }

    // If section doesn't exist in Canvas or format is wrong, return null
    return null;
}

/**
 * Merge Skyward and Canvas grades (Skyward takes priority, Canvas fills gaps)
 */
function mergeGradeData($skywardGrades, $canvasGrades)
{
    $mergedGrades = [];
    $processedKeys = [];

    echo "Merging " . count($skywardGrades) . " Skyward grades with " . count($canvasGrades) . " Canvas grades...\n";

    // Create lookup hash for Canvas grades to speed up matching
    echo "Creating Canvas grade lookup hash...\n";
    $canvasLookup = [];
    foreach ($canvasGrades as $canvasGrade) {
        $key = $canvasGrade['student_id'] . '|' . $canvasGrade['section_id'] . '|' . ($canvasGrade['term_code'] ?? '');
        $canvasLookup[$key] = $canvasGrade;
    }
    echo "Created lookup for " . count($canvasLookup) . " Canvas grades\n";

    // Process Skyward grades first (they take priority)
    $skywardCount = 0;
    foreach ($skywardGrades as $skywardGrade) {
        $skywardCount++;

        // Progress logging every 1000 records
        if ($skywardCount % 1000 === 0) {
            echo "Processed $skywardCount Skyward grades...\n";
        }
        // Skip rows with insufficient columns
        if (!is_array($skywardGrade) || count($skywardGrade) < 6) {
            continue;
        }

        $studentId = isset($skywardGrade[0]) ? $skywardGrade[0] : '';
        $sectionCode = isset($skywardGrade[2]) ? $skywardGrade[2] : '';
        $termCode = isset($skywardGrade[5]) ? $skywardGrade[5] : '';
        
        // Create unique key for student+section+term combination
        $key = $studentId . '|' . $sectionCode . '|' . $termCode;
        $processedKeys[$key] = true;

        // Use Skyward data, but fill in missing grades with Canvas if available
        $letterGrade = isset($skywardGrade[9]) ? $skywardGrade[9] : '';
        $percentage = isset($skywardGrade[10]) ? $skywardGrade[10] : '';
        
        // If Skyward grade is missing, try to fill from Canvas
        if (empty($letterGrade) || empty($percentage)) {
            $lookupKey = $studentId . '|' . $sectionCode . '|' . $termCode;
            $canvasMatch = isset($canvasLookup[$lookupKey]) ? $canvasLookup[$lookupKey] : null;
            if ($canvasMatch) {
                $letterGrade = $letterGrade ?: $canvasMatch['letter_grade'];
                $percentage = $percentage ?: $canvasMatch['grade'];
            }
        }

        $mergedGrades[] = [
            'student_id'   => $studentId,
            'course_code'  => isset($skywardGrade[1]) ? $skywardGrade[1] : '',
            'section_code' => $sectionCode,
            'school_code'  => isset($skywardGrade[3]) ? $skywardGrade[3] : '',
            'staff_id'     => isset($skywardGrade[4]) ? $skywardGrade[4] : '',
            'term_code'    => $termCode,
            'period'       => isset($skywardGrade[6]) ? $skywardGrade[6] : '',
            'class_name'   => isset($skywardGrade[7]) ? $skywardGrade[7] : '',
            'teacher_name' => isset($skywardGrade[8]) ? $skywardGrade[8] : '',
            'letter_grade' => $letterGrade,
            'percentage'   => $percentage,
            'performance'  => isset($skywardGrade[11]) ? $skywardGrade[11] : '',
            'source'       => 'skyward' . (($letterGrade !== (isset($skywardGrade[9]) ? $skywardGrade[9] : '') || $percentage !== (isset($skywardGrade[10]) ? $skywardGrade[10] : '')) ? '+canvas' : '')
        ];
    }

    // Add Canvas grades for student+section+term combinations not in Skyward
    foreach ($canvasGrades as $canvasGrade) {
        $studentId = $canvasGrade['student_id'];
        $sectionCode = $canvasGrade['section_id'];
        $termCode = $canvasGrade['term_code'] ?? '';
        
        $key = $studentId . '|' . $sectionCode . '|' . $termCode;
        
        // Only add if this combination wasn't processed from Skyward
        if (!isset($processedKeys[$key])) {
            $mergedGrades[] = [
                'student_id'   => $studentId,
                'course_code'  => $canvasGrade['course_id'] ?? '',
                'section_code' => $sectionCode,
                'school_code'  => '',
                'staff_id'     => '',
                'term_code'    => $termCode,
                'period'       => '',
                'class_name'   => '',
                'teacher_name' => '',
                'letter_grade' => $canvasGrade['letter_grade'],
                'percentage'   => $canvasGrade['grade'],
                'performance'  => '',
                'source'       => 'canvas'
            ];
        }
    }

    echo "Grade merge complete: " . count($mergedGrades) . " total merged grades\n";

    // Clean up memory
    $canvasLookup = null;
    unset($canvasLookup);

    return $mergedGrades;
}


/**
 * Batch insert grades into database
 */
function insertGradesBatch($db, $grades, $siteID, $currentSchoolYearID)
{
    if (empty($grades)) {
        return 0;
    }

    $valuesToImport = [];
    $rowCounter = 0;


    $dbColumns = "INSERT INTO abre_grades (
        student_id, course_code, section_code, school_code,
        staff_id, term_code, period, class_name, teacher_name, letter_grade,
        percentage, performance, site_id, school_year_id
    ) VALUES ";

    foreach ($grades as $grade) {
        $rowCounter++;


        $studentID = $db->escape_string($grade['student_id']);
        $courseCode = $db->escape_string($grade['course_code']);
        $sectionCode = $db->escape_string($grade['section_code']);
        $schoolCode = $db->escape_string($grade['school_code']);
        $staffID = $db->escape_string($grade['staff_id']);
        $termCode = $db->escape_string($grade['term_code']);
        $period = $db->escape_string($grade['period']);
        $className = $db->escape_string($grade['class_name'] ?? '');
        $teacherName = $db->escape_string($grade['teacher_name'] ?? '');
        $letterGrade = $db->escape_string($grade['letter_grade']);
        $percentage = $db->escape_string($grade['percentage']);
        $performance = $db->escape_string($grade['performance']);

        $valuesToImport[] = "(
            '$studentID', '$courseCode', '$sectionCode', '$schoolCode',
            '$staffID', '$termCode', '$period', '$className', '$teacherName',
            '$letterGrade', '$percentage', '$performance', $siteID, $currentSchoolYearID
        )";

        if (count($valuesToImport) >= MAX_IMPORT_LIMIT) {
            insertRows($db, $dbColumns, $valuesToImport);
            $valuesToImport = [];
        }
    }

    if (count($valuesToImport)) {
        insertRows($db, $dbColumns, $valuesToImport);
    }

    return $rowCounter;
}

/**
 * Batch insert assignments into database
 */
function insertAssignmentsBatch($db, $assignments, $siteID, $currentSchoolYearID)
{
    if (empty($assignments)) {
        echo "No assignments to insert\n";
        return 0;
    }

    echo "Starting assignment database insertion for " . count($assignments) . " assignments...\n";
    
    $valuesToImport = [];
    $rowCounter = 0;
    $batchCounter = 0;

    $dbColumns = "INSERT INTO abre_assignments (
        student_id, course_code, section_code, school_code,
        staff_id, term_code, period, title, description, due_date, category,
        earned_points, possible_points, weight_percentage, comment, published,
        site_id, school_year_id
    ) VALUES ";

    foreach ($assignments as $assignment) {
        $rowCounter++;

        $studentID = $db->escape_string('');
        $courseCode = $db->escape_string($assignment['course_id']);
        $sectionCode = $db->escape_string($assignment['section_id']);
        $schoolCode = $db->escape_string($assignment['school_code'] ?? '');
        $staffID = $db->escape_string($assignment['staff_id'] ?? '');
        $termCode = $db->escape_string($assignment['term_code'] ?? '');
        $period = $db->escape_string($assignment['period'] ?? '');
        $title = $db->escape_string($assignment['title']);
        $description = $db->escape_string($assignment['description']);
        $dueDate = $assignment['due_date'] ? "'" . $db->escape_string($assignment['due_date']) . "'" : 'NULL';
        $category = $db->escape_string($assignment['assignment_group']);
        $earnedPoints = !empty($assignment['earned_points']) ? "'" . $db->escape_string($assignment['earned_points']) . "'" : 'NULL';
        $possiblePoints = $db->escape_string($assignment['points_possible']);
        $weightPercentage = 'NULL';
        $comment = $db->escape_string('');
        $published = $db->escape_string($assignment['published']);

        $valuesToImport[] = "(
            '$studentID', '$courseCode', '$sectionCode', '$schoolCode',
            '$staffID', '$termCode', '$period', '$title', '$description',
            $dueDate, '$category', $earnedPoints, '$possiblePoints',
            $weightPercentage, '$comment', '$published', $siteID, $currentSchoolYearID
        )";

        if (count($valuesToImport) >= MAX_IMPORT_LIMIT) {
            $batchCounter++;
            echo "Inserting batch $batchCounter (" . count($valuesToImport) . " records)...\n";
            
            try {
                insertRows($db, $dbColumns, $valuesToImport);
                echo "Batch $batchCounter inserted successfully\n";
            } catch (Exception $e) {
                echo "Batch $batchCounter failed: " . $e->getMessage() . "\n";
                throw $e;
            }
            
            $valuesToImport = [];
        }
    }

    if (count($valuesToImport)) {
        $batchCounter++;
        echo "Inserting final batch $batchCounter (" . count($valuesToImport) . " records)...\n";
        
        try {
            insertRows($db, $dbColumns, $valuesToImport);
            echo "Final batch inserted successfully\n";
        } catch (Exception $e) {
            echo "Final batch failed: " . $e->getMessage() . "\n";
            throw $e;
        }
    }
    
    echo "Assignment insertion complete: $rowCounter assignments inserted in $batchCounter batches\n";
    return $rowCounter;
}

/**
 * Upload processed data to Google Cloud Storage
 */
function uploadToGCS($grades, $assignments, $gcsConfig, $siteID)
{
    try {
        $storage = new StorageClient(['projectId' => "abre-production"]);
        $bucketName = "prd-landing-zone";
        $currentDate = date("Ymd");
        $bucket = $storage->bucket($bucketName);

        // Upload grades JSON
        $gradesJsonEncoded = json_encode($grades);
        $type = 'grades';

        // Upload to site-id folder
        $tempFile1 = tmpfile();
        fwrite($tempFile1, $gradesJsonEncoded);
        rewind($tempFile1);
        $fileName = 'Abre_Canvas_Skyward_' . $type . '.json';
        $bucket->upload($tempFile1, [
            'name' => $currentDate . '/site-id/' . $siteID . '/' . $fileName
        ]);
        fclose($tempFile1);

        // Upload to filename folder
        $tempFile2 = tmpfile();
        fwrite($tempFile2, $gradesJsonEncoded);
        rewind($tempFile2);
        $folderName = 'Abre_Canvas_Skyward_' . $type;
        $bucket->upload($tempFile2, [
            'name' => $currentDate . '/filename/' . $folderName . '/' . $folderName . '-' . $siteID . '.json'
        ]);
        fclose($tempFile2);

        // Upload assignments JSON
        $assignmentsJsonEncoded = json_encode($assignments);
        $type = 'assignments';

        // Upload to site-id folder
        $tempFile3 = tmpfile();
        fwrite($tempFile3, $assignmentsJsonEncoded);
        rewind($tempFile3);
        $fileName = 'Abre_Canvas_Skyward_' . $type . '.json';
        $bucket->upload($tempFile3, [
            'name' => $currentDate . '/site-id/' . $siteID . '/' . $fileName
        ]);
        fclose($tempFile3);

        // Upload to filename folder
        $tempFile4 = tmpfile();
        fwrite($tempFile4, $assignmentsJsonEncoded);
        rewind($tempFile4);
        $folderName = 'Abre_Canvas_Skyward_' . $type;
        $bucket->upload($tempFile4, [
            'name' => $currentDate . '/filename/' . $folderName . '/' . $folderName . '-' . $siteID . '.json'
        ]);
        fclose($tempFile4);

    } catch (Exception $e) {
        error_log('GCS upload failed: ' . $e->getMessage());
        // Don't throw exception - GCS upload is optional
    }
}

/**
 * Batch fetch enrollments for multiple courses
 */
function batchFetchEnrollments($baseUrl, $accessToken, $courseIds)
{
    $allEnrollments = [];
    $batchSize = 20;
    $totalCourses = count($courseIds);
    
    echo "Processing $totalCourses courses in batches of $batchSize...\n";
    
    for ($i = 0; $i < $totalCourses; $i += $batchSize) {
        $batch = array_slice($courseIds, $i, $batchSize);
        $batchNum = floor($i / $batchSize) + 1;
        $totalBatches = ceil($totalCourses / $batchSize);
        
        echo "Processing batch $batchNum/$totalBatches (" . count($batch) . " courses)...\n";
        
        // Use cURL multi-handle for parallel requests
        $multiHandle = curl_multi_init();
        $curlHandles = [];
        
        foreach ($batch as $courseId) {
            $url = $baseUrl . '/api/v1/courses/' . $courseId . '/enrollments?' . http_build_query([
                'type[]'    => 'StudentEnrollment',
                'include[]' => 'grades',
                'per_page'  => 100
            ]);
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $accessToken,
                'Accept: application/json'
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            curl_multi_add_handle($multiHandle, $ch);
            $curlHandles[$courseId] = $ch;
        }
        
        // Execute all requests in parallel
        $running = null;
        do {
            curl_multi_exec($multiHandle, $running);
            curl_multi_select($multiHandle);
        } while ($running > 0);
        
        // Collect results
        foreach ($curlHandles as $courseId => $ch) {
            $response = curl_multi_getcontent($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            if ($httpCode === 200 && $response) {
                $data = json_decode($response, true);
                if (is_array($data)) {
                    $allEnrollments[$courseId] = $data;
                }
            } else {
                echo "Failed to fetch enrollments for course $courseId (HTTP $httpCode)\n";
                $allEnrollments[$courseId] = [];
            }
            
            curl_multi_remove_handle($multiHandle, $ch);
            curl_close($ch);
        }
        
        curl_multi_close($multiHandle);
        
        // Add small delay between batches to avoid rate limiting
        if ($i + $batchSize < $totalCourses) {
            usleep(500000);
        }
    }
    
    echo "Batch fetch complete! Got data for " . count($allEnrollments) . " courses\n";
    return $allEnrollments;
}

/**
 * Batch fetch assignments for multiple courses
 */
function batchFetchAssignments($baseUrl, $accessToken, $courseIds)
{
    $allAssignments = [];
    $batchSize = 20;
    $totalCourses = count($courseIds);
    
    echo "Processing $totalCourses courses in batches of $batchSize...\n";
    
    for ($i = 0; $i < $totalCourses; $i += $batchSize) {
        $batch = array_slice($courseIds, $i, $batchSize);
        $batchNum = floor($i / $batchSize) + 1;
        $totalBatches = ceil($totalCourses / $batchSize);
        
        echo "Processing batch $batchNum/$totalBatches (" . count($batch) . " courses)...\n";
        
        // Use cURL multi-handle for parallel requests
        $multiHandle = curl_multi_init();
        $curlHandles = [];
        
        foreach ($batch as $courseId) {
            $url = $baseUrl . '/api/v1/courses/' . $courseId . '/assignments?' . http_build_query([
                'per_page' => 100
            ]);
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $accessToken,
                'Accept: application/json'
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            curl_multi_add_handle($multiHandle, $ch);
            $curlHandles[$courseId] = $ch;
        }
        
        // Execute all requests in parallel
        $running = null;
        do {
            curl_multi_exec($multiHandle, $running);
            curl_multi_select($multiHandle);
        } while ($running > 0);
        
        // Collect results
        foreach ($curlHandles as $courseId => $ch) {
            $response = curl_multi_getcontent($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            if ($httpCode === 200 && $response) {
                $data = json_decode($response, true);
                if (is_array($data)) {
                    $allAssignments[$courseId] = $data;
                }
            } else {
                echo "Failed to fetch assignments for course $courseId (HTTP $httpCode)\n";
                $allAssignments[$courseId] = [];
            }
            
            curl_multi_remove_handle($multiHandle, $ch);
            curl_close($ch);
        }
        
        curl_multi_close($multiHandle);
        
        // Add small delay between batches to avoid rate limiting
        if ($i + $batchSize < $totalCourses) {
            usleep(500000);
        }
    }
    
    echo "Batch fetch complete! Got data for " . count($allAssignments) . " courses\n";
    return $allAssignments;
}

/**
 * Remove emojis from text
 */
function removeEmojis($text)
{
    // Remove emoji characters (Unicode ranges)
    $text = preg_replace('/[\x{1F600}-\x{1F64F}]/u', '', $text);
    $text = preg_replace('/[\x{1F300}-\x{1F5FF}]/u', '', $text);
    $text = preg_replace('/[\x{1F680}-\x{1F6FF}]/u', '', $text);
    $text = preg_replace('/[\x{1F1E0}-\x{1F1FF}]/u', '', $text);
    $text = preg_replace('/[\x{2600}-\x{26FF}]/u', '', $text);
    $text = preg_replace('/[\x{2700}-\x{27BF}]/u', '', $text); 
    return trim($text);
}

/**
 * Monitor memory usage
 */
function logMemoryUsage($message = "")
{
    $memory = memory_get_usage(true) / 1024 / 1024;
    $peakMemory = memory_get_peak_usage(true) / 1024 / 1024;
    echo "Memory: " . round($memory, 1) . "MB used, " . round($peakMemory, 1) . "MB peak" . ($message ? " - $message" : "") . "\n";
}

/**
 * Clean up old cache files
 */
function cleanupCache()
{
    $cacheDir = '/tmp/canvas_cache/';
    if (is_dir($cacheDir)) {
        $files = glob($cacheDir . '*.json');
        $cleaned = 0;
        foreach ($files as $file) {
            if (time() - filemtime($file) > 86400) {
                unlink($file);
                $cleaned++;
            }
        }
        if ($cleaned > 0) {
            echo "Cleaned up $cleaned old cache files\n";
        }
    }
}
