<?php

/*
* Copyright Abre.io Inc.
*/

// Enable garbage collection
if (function_exists('gc_enable')) {
    gc_enable();
}

// Check initial memory usage
$initialMemory = memory_get_usage(true) / 1024 / 1024;
if ($initialMemory > 100) {
    echo "WARNING: High initial memory usage ({$initialMemory}MB). Consider restarting PHP for best performance.\n";
}

require_once(dirname(__FILE__) . '/../../vendor/autoload.php');
require_once(dirname(__FILE__) . '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');

use Google\Cloud\Storage\StorageClient;
use phpseclib\Net\SFTP;
use GuzzleHttp\Client;

/**
 * Canvas API Client with centralized token management and automatic refresh
 */
class CanvasAPIClient
{
    private $baseUrl;
    private $accessToken;
    private $config;
    private $client;

    /**
     * Constructor
     */
    public function __construct($baseUrl, $config)
    {
        $this->baseUrl = $baseUrl;
        $this->config = $config;
        $this->accessToken = null;
        $this->client = null;
    }

    /**
     * Get base URL
     */
    public function getBaseUrl()
    {
        return $this->baseUrl;
    }

    /**
     * Get access token
     */
    public function getAccessToken()
    {
        return $this->accessToken;
    }

    /**
     * Update access token
     */
    public function updateAccessToken($newToken)
    {
        $this->accessToken = $newToken;
        // Reset client to use new token
        $this->client = null;
    }

    /**
     * Initialize the HTTP client
     */
    private function initClient()
    {
        $this->client = new Client([
            'timeout'         => CANVAS_API_TIMEOUT,
            'connect_timeout' => 10,
            'verify'          => true,
            'headers'         => [
                'Authorization' => 'Bearer ' . $this->accessToken,
                'Accept'        => 'application/json'
            ]
        ]);
    }

    /**
     * Get fresh access token
     */
    private function refreshToken()
    {
        echo "Attempting to refresh Canvas access token...\n";

        try {
            $client = new Client([
                'timeout' => CANVAS_API_TIMEOUT,
                'verify'  => true
            ]);

            $response = $client->post($this->config->baseUrl . '/login/oauth2/token', [
                'form_params' => [
                    'grant_type'    => 'refresh_token',
                    'client_id'     => $this->config->clientId,
                    'client_secret' => $this->config->clientSecret,
                    'refresh_token' => $this->config->refreshToken
                ]
            ]);

            $data = json_decode($response->getBody(), true);
            if (isset($data['access_token'])) {
                $this->accessToken = $data['access_token'];
                echo "Token refreshed successfully\n";
                $this->initClient();
                return true;
            } else {
                echo "Failed to refresh token - no access_token in response\n";
                return false;
            }

        } catch (Exception $e) {
            echo "Token refresh failed: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * Set initial access token
     */
    public function setAccessToken($token)
    {
        $this->accessToken = $token;
        $this->initClient();
    }

    /**
     * Make API request with automatic token refresh on 401
     */
    public function makeRequest($method, $endpoint, $params = [], $data = null)
    {
        // Rate limiting
        usleep(50000);

        if ($this->client === null) {
            $this->initClient();
        }

        $url = $this->baseUrl . '/api/v1' . $endpoint;
        if (!empty($params)) {
            $queryString = $this->buildQueryString($params);
            $url .= '?' . $queryString;
        }

        $options = [];
        if ($data) {
            $options['json'] = $data;
        }

        try {
            if ($this->client === null) {
                throw new Exception('Client not initialized');
            }
            $response = $this->client->request($method, $url, $options);

            if ($response->getStatusCode() === 401) {
                echo "401 Unauthorized - attempting to refresh token...\n";

                if ($this->refreshToken()) {
                    // Retry the request with new token
                    if ($this->client === null) {
                        throw new Exception('Client not initialized after token refresh');
                    }
                    $response = $this->client->request($method, $url, $options);

                    if ($response->getStatusCode() !== 200) {
                        echo "Request still failing after token refresh - HTTP {$response->getStatusCode()}\n";
                        return false;
                    }
                } else {
                    echo "Failed to refresh token\n";
                    return false;
                }
            }

            if ($response->getStatusCode() !== 200) {
                echo "Canvas API returned HTTP {$response->getStatusCode()} for: $endpoint\n";
                return false;
            }

            $responseData = json_decode($response->getBody(), true);
            return ['data' => $responseData, 'response' => $response];

        } catch (Exception $e) {
            // Check if it's a 401 error
            if (strpos($e->getMessage(), '401') !== false) {
                echo "401 error in exception - attempting to refresh token...\n";

                if ($this->refreshToken()) {
                    try {
                        // Retry the request
                        if ($this->client === null) {
                            throw new Exception('Client not initialized after token refresh');
                        }
                        $response = $this->client->request($method, $url, $options);
                        $responseData = json_decode($response->getBody(), true);
                        return ['data' => $responseData, 'response' => $response];
                    } catch (Exception $retryException) {
                        echo "Retry failed after token refresh: " . $retryException->getMessage() . "\n";
                        return false;
                    }
                } else {
                    echo "Failed to refresh token\n";
                    return false;
                }
            }

            echo "Canvas API request failed: $endpoint - " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * GET request
     */
    public function get($endpoint, $params = [])
    {
        return $this->makeRequest('GET', $endpoint, $params);
    }

    /**
     * POST request
     */
    public function post($endpoint, $data = null, $params = [])
    {
        return $this->makeRequest('POST', $endpoint, $params, $data);
    }

    /**
     * PUT request
     */
    public function put($endpoint, $data = null, $params = [])
    {
        return $this->makeRequest('PUT', $endpoint, $params, $data);
    }

    /**
     * Get all paginated data
     */
    public function getAllPaginatedData($endpoint, $params = [])
    {
        $allData = [];
        $currentEndpoint = $endpoint;
        $currentParams = $params;
        $pageCount = 0;
        $maxPages = 5000;

        do {
            $pageCount++;
            echo "Fetching page $pageCount...\n";

            $result = $this->get($currentEndpoint, $currentParams);

            if ($result === false) {
                echo "Failed to fetch page $pageCount - stopping pagination\n";
                break;
            }

            $data = $result['data'];
            $response = $result['response'];

            if (is_array($data)) {
                $allData = array_merge($allData, $data);
                // Only log every 5th page to reduce noise
                if ($pageCount % 5 === 0 || $pageCount === 1) {
                    echo "Page $pageCount: Added " . count($data) . " records (Total: " . count($allData) . ")\n";
                }
            }

            // Check for next page
            $linkHeader = $response->getHeader('Link');
            $nextUrl = null;

            if (!empty($linkHeader)) {
                $nextUrl = $this->getNextPageUrl($linkHeader[0]);
            }

            if ($nextUrl) {
                // Parse the next URL to get endpoint and params
                $parsedUrl = parse_url($nextUrl);
                $currentEndpoint = str_replace('/api/v1', '', $parsedUrl['path']);
                parse_str($parsedUrl['query'] ?? '', $currentParams);
            }

            // Safety check to prevent infinite loops
            if ($pageCount >= $maxPages) {
                echo "Reached maximum page limit ($maxPages), stopping pagination\n";
                break;
            }

        } while ($nextUrl);

        echo "Pagination complete: $pageCount pages, " . count($allData) . " total records\n";
        return $allData;
    }

    /**
     * Build query string properly for Canvas API with array parameters
     */
    private function buildQueryString($params)
    {
        $queryParts = [];

        foreach ($params as $key => $value) {
            if (is_array($value)) {
                // Handle array parameters like include[]
                foreach ($value as $arrayValue) {
                    $queryParts[] = urlencode($key) . '=' . urlencode($arrayValue);
                }
            } else {
                $queryParts[] = urlencode($key) . '=' . urlencode($value);
            }
        }

        return implode('&', $queryParts);
    }

    /**
     * Extract next page URL from Link header
     */
    private function getNextPageUrl($linkHeader)
    {
        $links = explode(',', $linkHeader);
        foreach ($links as $link) {
            if (strpos($link, 'rel="next"') !== false) {
                preg_match('/<(.*?)>/', $link, $matches);
                return $matches[1] ?? null;
            }
        }
        return null;
    }
}

/**
 * Main job function for Canvas-Skyward grades and assignments import
 */
function runJob($db, $siteID, $config)
{
    $cronName = 'Canvas Skyward Integration';
    $currentSchoolYearID = getCurrentSchoolYearID($db);
    $error = null;
    $skip = null;

    $uuid = Logger::logCronStart($db, $siteID, $cronName);

    try {

        // Define constants
        define('SAFE_COLUMN_COUNT', 12);
        define('MAX_IMPORT_LIMIT', 25);
        define('CANVAS_API_TIMEOUT', 30);

        // Initialize variables
        $error = null;
        $skip = null;
        $separator = "\r\n";

        // Initialize Canvas API client
        echo "Initializing Canvas API client...\n";
        $canvasClient = new CanvasAPIClient($config->configs->canvas->baseUrl, $config->configs->canvas);

        // Get initial access token
        $accessToken = getCanvasAccessToken($config->configs->canvas);
        
        // Set global canvas client for token refresh
        $GLOBALS['canvas_client'] = $canvasClient;
        if (!$accessToken) {
            echo "Authentication failed - exiting\n";
            throw new Exception('Failed to authenticate with Canvas API');
        }

        // Set the access token in the client
        $canvasClient->setAccessToken($accessToken);
        echo "Got access token: " . substr($accessToken, 0, 20) . "...\n";

        // Set global variables for token management
        $GLOBALS['canvas_access_token'] = $accessToken;
        $GLOBALS['canvas_config'] = $config->configs->canvas;

        // Get Canvas accounts and grading periods
        $accounts = getCanvasAccounts($canvasClient);
        if (empty($accounts)) {
            throw new Exception('No Canvas accounts found');
        }

        $accountId = $accounts[0]['id'];
        
        // We'll use the grading periods from CSV mapping instead of API
        echo "Using grading periods from CSV mapping\n";

        // Check if SFTP configuration exists
        if (isset($config->sftp) && isset($config->sftp->ip)) {
            // Connect to SFTP (production pattern)
            echo "Connecting to SFTP...\n";
            $sftp = new SFTP($config->sftp->ip);
            if (!$sftp->login($config->sftp->userName, $config->sftp->password)) {
                throw new Exception("SFTP login failed.");
            }

            echo "Reading Skyward files from SFTP...\n";
            $skywardData = processSkywardFiles($sftp, $separator);
        } else {
            // Fallback to local files for testing/development
            echo "SFTP config not found, reading Skyward files from local Documents folder...\n";
            $skywardData = processSkywardFilesLocal($separator);
        }

        if (empty($skywardData)) {
            $skip = true;
            throw new Exception('No Skyward files found or empty files.');
        }

        // Memory check before starting
        logMemoryUsage("Script start");

        // Safety check - don't start if memory is already high
        $initialMemory = memory_get_usage(true) / 1024 / 1024;
        if ($initialMemory > 500) {
            echo "High initial memory usage detected: {$initialMemory}MB - consider restarting PHP process\n";
        }

        // Build Skyward sections mapping before processing
        $skywardSections = [];
        $totalGradeRecords = count($skywardData['grades'] ?? []);

        if ($totalGradeRecords > 0) {
            echo "Processing $totalGradeRecords grade records from Skyward\n";
        }

        if (!empty($skywardData['grades'])) {
            $validRecords = 0;
            $emptySections = 0;
            foreach ($skywardData['grades'] as $grade) {
                if (is_array($grade) && count($grade) >= 7) {
                    $sectionCode = isset($grade[2]) ? $grade[2] : '';
                    $schoolCode = isset($grade[3]) ? $grade[3] : '';
                    $staffId = isset($grade[4]) ? $grade[4] : '';
                    $termCode = isset($grade[5]) ? $grade[5] : '';
                    $period = isset($grade[6]) ? $grade[6] : '';

                    if (!empty($sectionCode)) {
                        $validRecords++;
                        // Store Skyward section metadata (only once per section)
                        if (!isset($skywardSections[$sectionCode])) {
                            $skywardSections[$sectionCode] = [
                                'section_code' => $sectionCode,
                                'school_code'  => $schoolCode,
                                'staff_id'     => $staffId,
                                'term_code'    => $termCode,
                                'period'       => $period,
                                'course_code'  => isset($grade[1]) ? $grade[1] : ''
                            ];
                        }
                    } else {
                        $emptySections++;
                    }
                }
            }

            if ($emptySections > 0) {
                echo "Warning: $emptySections grade records had empty section codes\n";
            }
        }

        echo "Found " . count($skywardSections) . " unique sections from Skyward data\n";

        // Start database transaction
        echo "\n=== DATABASE OPERATIONS ===\n";
        echo "Starting database transaction...\n";
        $db->begin_transaction();

        try {
            // Clear existing data
            echo "Clearing existing data for site $siteID, school year $currentSchoolYearID...\n";
            $db->query("DELETE FROM abre_grades WHERE site_id = $siteID AND school_year_id = $currentSchoolYearID");
            $db->query("DELETE FROM abre_assignments WHERE site_id = $siteID AND school_year_id = $currentSchoolYearID");

            echo "Existing data cleared\n";

            // Process data in smaller batches to reduce memory usage
            echo "\n=== BATCH PROCESSING DATA ===\n";
            $totalGradesInserted = 0;
            $totalAssignmentsInserted = 0;

            // Process sections in batches of 20 to reduce memory usage
            $sectionBatchSize = 20;
            $sectionKeys = array_keys($skywardSections);
            $totalSections = count($sectionKeys);

            for ($i = 0; $i < $totalSections; $i += $sectionBatchSize) {
                $batchSectionKeys = array_slice($sectionKeys, $i, $sectionBatchSize);
                $batchSections = [];
                foreach ($batchSectionKeys as $sectionKey) {
                    $batchSections[$sectionKey] = $skywardSections[$sectionKey];
                }

                $batchNum = floor($i / $sectionBatchSize) + 1;
                $totalBatches = ceil($totalSections / $sectionBatchSize);

                echo "\n--- Processing batch $batchNum/$totalBatches (" . count($batchSections) . " sections) ---\n";
                logMemoryUsage("Before batch processing");

                // Process Canvas grades for this batch
                echo "Processing Canvas grades for batch...\n";
                $canvasGrades = processCanvasGrades($canvasClient, $skywardData, $accountId, $batchSections);
                echo "Canvas grades batch result: " . count($canvasGrades) . " grade records\n";

                // Filter Skyward grades for this batch only
                $batchSkywardGrades = filterSkywardGradesForSections($skywardData['grades'], $batchSections);

                // Merge grades data for this batch
                echo "Merging grade data for batch...\n";
                $mergedGrades = mergeGradeData($batchSkywardGrades, $canvasGrades);
                echo "Merged grades batch result: " . count($mergedGrades) . " total grade records\n";

                // Insert grades for this batch (now includes GCS upload)
                echo "Inserting grades for batch...\n";
                $batchGradesInserted = insertGradesBatch($db, $mergedGrades, $siteID, $currentSchoolYearID, $config->gcs ?? null, $batchNum);
                $totalGradesInserted += $batchGradesInserted;
                echo "Batch grades inserted: $batchGradesInserted (Total: $totalGradesInserted)\n";

                // Process Canvas assignments for this batch (inserts during processing and uploads to GCS)
                echo "Processing Canvas assignments for batch...\n";
                $batchAssignmentsResult = processCanvasAssignments($config->canvas->baseUrl, $accessToken, $skywardData, $db, $siteID, $currentSchoolYearID, $accountId, $batchSections, $config->gcs ?? null, $batchNum);
                $batchAssignmentsInserted = $batchAssignmentsResult['inserted'];
                $totalAssignmentsInserted += $batchAssignmentsInserted;
                echo "Batch assignments inserted: $batchAssignmentsInserted (Total: $totalAssignmentsInserted)\n";

                // Memory cleanup after each batch
                echo "Cleaning up memory after batch...\n";
                $canvasGrades = null;
                $mergedGrades = null;
                $batchSections = null;
                $batchSkywardGrades = null;
                unset($canvasGrades, $mergedGrades, $batchSections, $batchSkywardGrades);

                // Force garbage collection
                if (function_exists('gc_collect_cycles')) {
                    gc_collect_cycles();
                }
                logMemoryUsage("After batch cleanup");

                // Small delay between batches to allow system recovery
                usleep(100000);
            }

            // Set final totals
            $gradesInserted = $totalGradesInserted;
            $assignmentsInserted = $totalAssignmentsInserted;

            // Memory cleanup (no need for final GCS upload - done incrementally per batch)
            echo "\nPerforming memory cleanup...\n";
            logMemoryUsage("Before cleanup");
            $canvasGrades = null;
            $mergedGrades = null;
            $skywardData = null;
            $allEnrollments = null;
            unset($canvasGrades, $mergedGrades, $skywardData, $allEnrollments);

            // Force garbage collection
            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }
            logMemoryUsage("After cleanup");
            echo "Memory cleanup complete\n";

            // Commit transaction
            echo "\nCommitting transaction...\n";
            $db->commit();
            echo "Transaction committed successfully\n";

        } catch (Exception $e) {
            $db->rollback();
            throw new Exception('Database operation failed: ' . $e->getMessage());
        }

    } catch (Exception $ex) {
        $error = $ex->getMessage();
    }

    $details = [];
    if(isset($error) && !is_null($error)){
        $details["error"] = $error;
        $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
    }else{
        $details = [
            'gradesInserted'      => $gradesInserted ?? 0,
            'assignmentsInserted' => $assignmentsInserted ?? 0,
            'skywardRecords'      => count($skywardData['grades'] ?? []),
            'canvasRecords'       => count($canvasGrades ?? [])
        ];
        $status = CRON_SUCCESS;
    }

    Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}

/**
 * Get Canvas API access token using OAuth2 client credentials flow
 */
function getCanvasAccessToken($canvasConfig)
{
    echo "Attempting Canvas authentication...\n";
    try {
        $client = new Client([
            'timeout' => CANVAS_API_TIMEOUT,
            'verify'  => true
        ]);

        $response = $client->post($canvasConfig->baseUrl . '/login/oauth2/token', [
            'form_params' => [
                'grant_type'    => 'refresh_token',
                'client_id'     => $canvasConfig->clientId,
                'client_secret' => $canvasConfig->clientSecret,
                'refresh_token' => $canvasConfig->refreshToken
            ]
        ]);

        $data = json_decode($response->getBody(), true);
        if (isset($data['access_token'])) {
            echo "Authentication successful!\n";
            return $data['access_token'];
        } else {
            echo "No access token in response\n";
            echo "Response: " . json_encode($data) . "\n";
            return null;
        }

    } catch (Exception $e) {
        echo "Canvas API authentication failed: " . $e->getMessage() . "\n";
        return null;
    }
}

/**
 * Get the current Canvas access token (may refresh if needed)
 */
function getCurrentCanvasAccessToken()
{
    if (isset($GLOBALS['canvas_access_token']) && !empty($GLOBALS['canvas_access_token'])) {
        return $GLOBALS['canvas_access_token'];
    }
    
    // If no token or empty, try to get a fresh one
    if (isset($GLOBALS['canvas_config'])) {
        $newToken = getCanvasAccessToken($GLOBALS['canvas_config']);
        if ($newToken) {
            $GLOBALS['canvas_access_token'] = $newToken;
            return $newToken;
        }
    }
    
    return null;
}

/**
 * Generic Canvas API request handler with rate limiting and auto token refresh
 */
function makeCanvasAPIRequest($baseUrl, $accessToken, $endpoint, $params = [])
{
    // Add rate limiting to prevent API throttling
    usleep(50000);

    try {
        $client = new Client([
            'timeout'         => 30,
            'connect_timeout' => 10,
            'verify'          => true,
            'headers'         => [
                'Authorization' => 'Bearer ' . $accessToken,
                'Accept'        => 'application/json'
            ]
        ]);

        $url = $baseUrl . '/api/v1' . $endpoint;
        if (!empty($params)) {
            // Handle array parameters properly for Canvas API
            $queryString = buildCanvasQueryString($params);
            $url .= '?' . $queryString;
        }

        // Only log API requests for debugging - comment out for production
        // echo "API Request: $url\n";
        $response = $client->get($url);

        if ($response->getStatusCode() === 401) {
            echo "401 Unauthorized - attempting to refresh token...\n";
            
            // Try to refresh the token if we have the config
            if (isset($GLOBALS['canvas_config'])) {
                $newToken = getCanvasAccessToken($GLOBALS['canvas_config']);
                if ($newToken) {
                    echo "Token refreshed successfully, retrying request...\n";
                    $GLOBALS['canvas_access_token'] = $newToken;
                    // Update the CanvasAPIClient object with the new token
                    if (isset($GLOBALS['canvas_client'])) {
                        $GLOBALS['canvas_client']->updateAccessToken($newToken);
                    }
                    
                    // Retry the request with the new token
                    $client = new Client([
                        'timeout'         => 30,
                        'connect_timeout' => 10,
                        'verify'          => true,
                        'headers'         => [
                            'Authorization' => 'Bearer ' . $newToken,
                            'Accept'        => 'application/json'
                        ]
                    ]);
                    
                    $response = $client->get($url);
                    
                    if ($response->getStatusCode() !== 200) {
                        echo "Canvas API still failing after token refresh - HTTP {$response->getStatusCode()} for: $endpoint\n";
                        $body = $response->getBody()->getContents();
                        echo "Response body: $body\n";
                        return false;
                    }
                } else {
                    echo "Failed to refresh token\n";
                    return false;
                }
            } else {
                echo "No Canvas config available for token refresh\n";
                return false;
            }
        } else if ($response->getStatusCode() !== 200) {
            echo "Canvas API returned HTTP {$response->getStatusCode()} for: $endpoint\n";
            $body = $response->getBody()->getContents();
            echo "Response body: $body\n";
            return false;
        }

        $data = json_decode($response->getBody(), true);
        return ['data' => $data, 'response' => $response];

    } catch (Exception $e) {
        // Check if it's a 401 error in the exception message
        if (strpos($e->getMessage(), '401') !== false && isset($GLOBALS['canvas_config'])) {
            echo "401 error detected in exception - attempting to refresh token...\n";
            
            $newToken = getCanvasAccessToken($GLOBALS['canvas_config']);
            if ($newToken) {
                echo "Token refreshed successfully, retrying request...\n";
                $GLOBALS['canvas_access_token'] = $newToken;
                
                // Retry the request with the new token
                try {
                    $client = new Client([
                        'timeout'         => 30,
                        'connect_timeout' => 10,
                        'verify'          => true,
                        'headers'         => [
                            'Authorization' => 'Bearer ' . $newToken,
                            'Accept'        => 'application/json'
                        ]
                    ]);
                    
                    $response = $client->get($url);
                    
                    if ($response->getStatusCode() !== 200) {
                        echo "Canvas API still failing after token refresh - HTTP {$response->getStatusCode()} for: $endpoint\n";
                        return false;
                    }
                    
                    $data = json_decode($response->getBody(), true);
                    return ['data' => $data, 'response' => $response];
                    
                } catch (Exception $retryE) {
                    echo "Retry after token refresh also failed: " . $retryE->getMessage() . "\n";
                    return false;
                }
            }
        }
        
        echo "Canvas API request failed: $endpoint - " . $e->getMessage() . "\n";
        return false;
    }
}

/**
 * Build query string properly for Canvas API with array parameters
 */
function buildCanvasQueryString($params)
{
    $queryParts = [];

    foreach ($params as $key => $value) {
        if (is_array($value)) {
            // Handle array parameters like include[]
            foreach ($value as $arrayValue) {
                $queryParts[] = urlencode($key) . '=' . urlencode($arrayValue);
            }
        } else {
            $queryParts[] = urlencode($key) . '=' . urlencode($value);
        }
    }

    return implode('&', $queryParts);
}

/**
 * Get all paginated data from Canvas API
 */
function getAllPaginatedCanvasData($baseUrl, $accessToken, $endpoint, $params = [])
{
    $allData = [];
    $currentEndpoint = $endpoint;
    $currentParams = $params;
    $pageCount = 0;
    $maxPages = 5000;
    
    do {
        $pageCount++;
        echo "Fetching page $pageCount...\n";
        
        $result = makeCanvasAPIRequest($baseUrl, $accessToken, $currentEndpoint, $currentParams);
        
        if ($result === false) {
            echo "Failed to fetch page $pageCount - stopping pagination\n";
            break;
        }
        
        $data = $result['data'];
        $response = $result['response'];
        
        if (is_array($data)) {
            $allData = array_merge($allData, $data);
            // Only log every 5th page to reduce noise
            if ($pageCount % 5 === 0 || $pageCount === 1) {
                echo "Page $pageCount: Added " . count($data) . " records (Total: " . count($allData) . ")\n";
            }
        }
        
        // Check for next page
        $linkHeader = $response->getHeader('Link');
        $nextUrl = null;
        
        if (!empty($linkHeader)) {
            $nextUrl = getNextPageUrl($linkHeader[0]);
        }
        
        if ($nextUrl) {
            // Parse the next URL to get endpoint and params
            $parsedUrl = parse_url($nextUrl);
            $currentEndpoint = str_replace('/api/v1', '', $parsedUrl['path']);
            parse_str($parsedUrl['query'] ?? '', $currentParams);
        }
        
        // Safety check to prevent infinite loops
        if ($pageCount >= $maxPages) {
            echo "Reached maximum page limit ($maxPages), stopping pagination\n";
            break;
        }
        
    } while ($nextUrl);
    
    echo "Pagination complete: $pageCount pages, " . count($allData) . " total records\n";
    return $allData;
}

/**
 * Extract next page URL from Link header
 */
function getNextPageUrl($linkHeader)
{
    $links = explode(',', $linkHeader);
    foreach ($links as $link) {
        if (strpos($link, 'rel="next"') !== false) {
            preg_match('/<(.*?)>/', $link, $matches);
            return $matches[1] ?? null;
        }
    }
    return null;
}

/**
 * Get Canvas accounts
 */
function getCanvasAccounts($canvasClient)
{
    $result = $canvasClient->get('/accounts');
    return $result === false ? false : $result['data'];
}

/**
 * Get Canvas terms for an account
 */
function getCanvasTerms($baseUrl, $accessToken, $accountId)
{
    $result = makeCanvasAPIRequest($baseUrl, $accessToken, "/accounts/$accountId/terms");
    return $result === false ? false : $result['data'];
}

/**
 * Get Canvas grading periods for an account (more granular than terms)
 */
function getCanvasGradingPeriods($baseUrl, $accessToken, $accountId)
{
    $result = makeCanvasAPIRequest($baseUrl, $accessToken, "/accounts/$accountId/grading_periods");
    return $result === false ? false : $result['data'];
}

/**
 * Get Canvas courses for a term
 */
function getCanvasCourses($baseUrl, $accessToken, $termId)
{
    return getAllPaginatedCanvasData($baseUrl, $accessToken, '/courses', [
        'enrollment_term_id' => $termId,
        'with_enrollments'   => 'true',
        'per_page'           => 100
    ]);
}

/**
 * Get Canvas courses for terms specified in term mapping
 */
function getCanvasCoursesForMappedTerms($baseUrl, $accessToken, $accountId, $termMapping)
{
    $allCourses = [];
    
    if (empty($termMapping)) {
        echo "No term mapping found, getting all courses\n";
        return getAllPaginatedCanvasData($baseUrl, $accessToken, "/accounts/$accountId/courses", [
            'per_page'        => 100,
            'include'         => ['syllabus_body', 'total_scores', 'course_image', 'public_description', 'public_syllabus'],
            'enrollment_type' => 'teacher',
            'enrollment_role' => 'teacher'
        ]);
    }
    
    // Get unique term IDs from the built term mapping
    $termIds = [];
    foreach ($termMapping as $gradingPeriodId) {
        // Use the grading period ID as the term ID
        if (!empty($gradingPeriodId) && !in_array($gradingPeriodId, $termIds)) {
            $termIds[] = $gradingPeriodId;
        }
    }
    
    echo "Processing " . count($termIds) . " terms from mapping: " . implode(', ', $termIds) . "\n";
    
    // Get courses for each term
    foreach ($termIds as $termId) {
        echo "Getting courses for term ID: $termId\n";
        $termCourses = getAllPaginatedCanvasData($baseUrl, $accessToken, "/accounts/$accountId/courses", [
            'per_page'           => 100,
            'include'            => ['syllabus_body', 'total_scores', 'course_image', 'public_description', 'public_syllabus'],
            'enrollment_type'    => 'teacher',
            'enrollment_role'    => 'teacher',
            'enrollment_term_id' => $termId
        ]);
        
        if (!empty($termCourses)) {
            $allCourses = array_merge($allCourses, $termCourses);
            echo "Found " . count($termCourses) . " courses for term $termId\n";
        }
    }
    
    echo "Total courses found across all mapped terms: " . count($allCourses) . "\n";
    return $allCourses;
}

/**
 * Get Canvas courses for grading periods specified in mapping
 * NOTE: Since grading_period_id doesn't filter courses, we fetch all courses once
 * and filter by enrollment_term_id in the application logic instead.
 */
function getCanvasCoursesForMappedGradingPeriods($canvasClient, $accountId, $gradingPeriodMapping)
{
    // Build readable list of grading periods with names for logging
    $gradingPeriodList = [];
    foreach ($gradingPeriodMapping as $gradingPeriodId => $mappingData) {
        $termName = $mappingData['canvas_title'] ?? 'Unknown';
        $gradingPeriodList[] = "$termName ($gradingPeriodId)";
    }
    echo "Processing " . count($gradingPeriodMapping) . " grading periods: " . implode(', ', $gradingPeriodList) . "\n";
    echo "OPTIMIZATION: Fetching all courses once (grading_period_id doesn't filter courses in Canvas API)\n";

    // Get all courses once - much more efficient than multiple API calls
    $allCourses = $canvasClient->getAllPaginatedData("/accounts/$accountId/courses", [
        'per_page'        => 100,
        'include'         => ['syllabus_body', 'total_scores', 'course_image', 'public_description', 'public_syllabus'],
        'enrollment_type' => 'teacher',
        'enrollment_role' => 'teacher'
        // NOTE: Removed grading_period_id as it doesn't filter courses in Canvas API
    ]);
    
    echo "Found " . count($allCourses) . " total courses (will be filtered by enrollment_term_id in processing)\n";
    return $allCourses;
}

/**
 * Get Canvas sections for a course
 */
function getCanvasSections($baseUrl, $accessToken, $courseId)
{
    return getAllPaginatedCanvasData($baseUrl, $accessToken, "/courses/$courseId/sections", [
        'per_page' => 100
    ]);
}

/**
 * Get Canvas enrollments with grades for a section
 */
function getCanvasEnrollments($baseUrl, $accessToken, $courseId)
{
    try {
        $result = getAllPaginatedCanvasData($baseUrl, $accessToken, "/courses/$courseId/enrollments", [
            'type[]'    => 'StudentEnrollment',
            'include[]' => 'grades',
            'per_page'  => 100
        ]);

        if ($result === false) {
            echo "Canvas API returned false for course $courseId\n";
            return false;
        }

        if (!is_array($result)) {
            echo "Canvas API returned non-array result for course $courseId: " . gettype($result) . "\n";
            return false;
        }

        return $result;

    } catch (Exception $e) {
        echo "Exception in getCanvasEnrollments for course $courseId: " . $e->getMessage() . "\n";
        return false;
    }
}

/**
 * Get Canvas assignments for a course
 */
function getCanvasAssignments($baseUrl, $accessToken, $courseId)
{
    return getAllPaginatedCanvasData($baseUrl, $accessToken, "/courses/$courseId/assignments", [
        'per_page' => 100
    ]);
}

/**
 * Get Canvas assignment groups for a course
 */
function getCanvasAssignmentGroups($baseUrl, $accessToken, $courseId)
{
    return getAllPaginatedCanvasData($baseUrl, $accessToken, "/courses/$courseId/assignment_groups", [
        'per_page' => 100
    ]);
}

/**
 * Get Canvas submissions for assignments with user data
 */
function getCanvasSubmissions($baseUrl, $accessToken, $courseId, $studentIds = [])
{
    // Build parameters correctly for Canvas API
    $params = [
        'student_ids[]' => 'all',
        'include[]'     => ['assignment', 'submission_comments', 'user'],
        'per_page'      => 100
    ];

    if (!empty($studentIds)) {
        $params['student_ids[]'] = implode(',', $studentIds);
    }

    return getAllPaginatedCanvasData($baseUrl, $accessToken, "/courses/$courseId/students/submissions", $params);
}

/**
 * Get student integration ID from Canvas user data
 */
function getStudentIntegrationId($submission)
{
    // Try to get integration_id first (preferred)
    if (isset($submission['user']['integration_id']) && !empty($submission['user']['integration_id'])) {
        return $submission['user']['integration_id'];
    }

    // Fallback to sis_user_id
    if (isset($submission['user']['sis_user_id']) && !empty($submission['user']['sis_user_id'])) {
        return $submission['user']['sis_user_id'];
    }

    // Last resort: Canvas user ID (not ideal for Skyward integration)
    return $submission['user_id'] ?? '';
}

/**
 * Process Skyward files from SFTP
 */
function processSkywardFiles($sftp, $separator)
{
    $data = ['grades' => [], 'sections' => [], 'term_map' => []];

    // Read Abre_Grades_2.0.csv (updated filename)
    $gradesFile = $sftp->get('Abre_Grades_2.0.csv');
    if ($gradesFile) {
        $data['grades'] = parseCSVFile($gradesFile, $separator, ',');
    }

    // Read canvas_term_map.csv (term mapping data)
    $termMapFile = $sftp->get('canvas_term_map.csv');
    if ($termMapFile) {
        $data['term_map'] = parseCSVFile($termMapFile, $separator, ',');
    }

    return $data;
}

/**
 * Parse CSV file content
 */
function parseCSVFile($fileContent, $lineSeparator, $columnSeparator, $minColumnCount = 1)
{
    $data = [];
    $lines = explode($lineSeparator, trim($fileContent));

    if (empty($lines)) {
        return $data;
    }

    // Skip header row
    array_shift($lines);

    foreach ($lines as $line) {
        if (empty(trim($line))) {
            continue;
        }

        $row = str_getcsv($line, $columnSeparator);
        if (count($row) >= $minColumnCount) {
            $data[] = $row;
        }
    }

    return $data;
}

/**
 * Process Canvas grades for sections using grading period mapping
 */
function processCanvasGrades($canvasClient, $skywardData, $accountId, $skywardSections = [])
{
    $canvasGrades = [];

    // Build grading period mapping from canvas_term_map.csv data
    $gradingPeriodMapping = buildMapping($skywardData['term_map'] ?? []);
    echo "Getting Canvas courses for mapped grading periods from API...\n";
    echo "Base grading periods from CSV: " . count($gradingPeriodMapping) . "\n";
    
    // Use base grading periods directly instead of school-specific terms
    echo "Using base grading periods directly: " . count($gradingPeriodMapping) . "\n";

    // Get Canvas courses for base grading periods (much more efficient)
    $allCourses = getCanvasCoursesForMappedGradingPeriods($canvasClient, $accountId, $gradingPeriodMapping);
    if (empty($allCourses)) {
        echo "No Canvas courses found for mapped grading periods\n";
        return $canvasGrades;
    }
    
    // Extract course IDs from Canvas API data
    $courseIds = [];
    foreach ($allCourses as $course) {
        if (isset($course['id']) && !in_array($course['id'], $courseIds)) {
            $courseIds[] = $course['id'];
        }
    }
    
    echo "Found " . count($courseIds) . " Canvas courses to process\n";

    // Filter enrollments to only process courses that match Skyward sections
    $enrollmentFilteredCourseIds = [];
    $sectionMatchCounts = [];

    foreach ($courseIds as $courseId) {
        // Get course info to match with Skyward sections
        foreach ($allCourses as $course) {
            if ($course['id'] == $courseId) {
                $courseTermId = $course['enrollment_term_id'] ?? null;
                $courseTermCode = '';
                if ($courseTermId && isset($gradingPeriodMapping[$courseTermId])) {
                    $courseTermCode = $gradingPeriodMapping[$courseTermId]['term_code'];
                }

                // Check if this course has matching Skyward sections
                $matchingSections = [];
                foreach ($skywardSections as $skywardSection) {
                    // Match by term code
                    if ($skywardSection['term_code'] === $courseTermCode) {
                        $matchingSections[] = $skywardSection;
                    }
                }

                if (!empty($matchingSections)) {
                    $enrollmentFilteredCourseIds[] = $courseId;
                    // Count matches per section
                    foreach ($matchingSections as $section) {
                        $sectionId = $section['section_code'];
                        $sectionMatchCounts[$sectionId] = ($sectionMatchCounts[$sectionId] ?? 0) + 1;
                    }
                }
                break;
            }
        }
    }

    echo "Filtered enrollments to " . count($enrollmentFilteredCourseIds) . " courses that match Skyward sections\n";

    // Batch fetch enrollments for filtered courses only
    echo "Batch fetching enrollments for " . count($enrollmentFilteredCourseIds) . " filtered courses...\n";
    $allEnrollments = batchFetchEnrollments($canvasClient, $enrollmentFilteredCourseIds);
    echo "Fetched enrollments for " . count($allEnrollments) . " courses\n";

    // Process filtered courses using pre-fetched data
    echo "Processing " . count($enrollmentFilteredCourseIds) . " filtered courses with pre-fetched data...\n";
    $processedCourses = 0;
    $totalCoursesToProcess = count($enrollmentFilteredCourseIds);

    foreach ($enrollmentFilteredCourseIds as $courseId) {
        $processedCourses++;
        
        // Only log every 50th course to reduce noise
        if ($processedCourses % 50 === 0 || $processedCourses <= 5) {
            echo "Processing course $processedCourses/$totalCoursesToProcess: Course ID $courseId\n";
        }

        // Get enrollments from batch-fetched data (no API call needed!)
        $enrollments = $allEnrollments[$courseId] ?? [];

        // Periodic memory cleanup during enrollment processing
        if ($processedCourses % 25 === 0) {
            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }
            logMemoryUsage("Enrollment processing cleanup after $processedCourses courses");
        }

        foreach ($enrollments as $enrollment) {
            if ($enrollment['type'] === 'StudentEnrollment' && isset($enrollment['grades'])) {
                $studentId = $enrollment['user']['integration_id'] ?? $enrollment['user']['sis_user_id'];

                if ($studentId) {
                    // Get term code from mapping if available
                    $termCode = getTermCodeFromMapping($enrollment, $gradingPeriodMapping);
                    
                    // Use course ID as section identifier since we're processing all Canvas courses
                    $canvasGrades[] = [
                        'student_id'   => $studentId,
                        'section_id'   => $courseId,
                        'course_id'    => $courseId,
                        'grade'        => $enrollment['grades']['current_score'] ?? null,
                        'letter_grade' => $enrollment['grades']['current_grade'] ?? null,
                        'term_code'    => $termCode
                    ];
                }
            }
        }
    }

    echo "Processed $processedCourses courses with Canvas data\n";

    return $canvasGrades;
}

/**
 * Build mapping array from canvas_term_map.csv data
 * Generic function that can be used for both term and grading period mappings
 */
function buildMapping($termMapData)
{
    $mapping = [];
    
    foreach ($termMapData as $row) {
        if (count($row) >= 5) {
            $termCode = $row[0];
            $canvasTitle = $row[1];
            $gradingPeriodId = $row[2];
            $startDate = $row[3];
            $endDate = $row[4];
            
            $mapping[$gradingPeriodId] = [
                'term_code'    => $termCode,
                'canvas_title' => $canvasTitle,
                'start_date'   => $startDate,
                'end_date'     => $endDate
            ];
        }
    }
    
    return $mapping;
}

/**
 * Generate school-specific terms by duplicating base terms for each school
 */
function generateSchoolSpecificTerms($baseTerms)
{
    $schools = [
        'Wayzata Central Middle School',
        'Wayzata East Middle School',
        'Wayzata West Middle School',
        'Wayzata High School',
        'Wayzata Transition School',
        'North Woods Elementary',
        'Meadow Ridge Elementary',
        'Kimberly Lane Elementary',
        'Gleason Lake Elementary',
        'Plymouth Creek Elementary',
        'Sunset Hill Elementary',
        'Oakwood Elementary',
        'Greenwood Elementary',
        'Birchview Elementary',
        'ALC - INDEPENDENT STUDY',
        'Extended School Year'
    ];

    $schoolSpecificTerms = [];
    $baseId = 1000;

    foreach ($schools as $school) {
        foreach ($baseTerms as $termId => $termData) {
            $newId = $baseId++;
            $schoolSpecificTerms[$newId] = [
                'term_code'    => $termData['term_code'],
                'canvas_title' => $termData['canvas_title'],
                'start_date'   => $termData['start_date'],
                'end_date'     => $termData['end_date'],
                'school'       => $school,
                'original_id'  => $termId
            ];
        }
    }

    return $schoolSpecificTerms;
}

/**
 * Get term code from Canvas enrollment using term mapping
 */
function getTermCodeFromMapping($enrollment, $termMapping)
{
    // Try to get grading period from enrollment
    if (isset($enrollment['grades']['grading_period_id'])) {
        $gradingPeriodId = $enrollment['grades']['grading_period_id'];
        if (isset($termMapping[$gradingPeriodId])) {
            return $termMapping[$gradingPeriodId]['term_code'];
        }
    }
    
    return '';
}

/**
 * Process Canvas assignments and submissions with parallel batch processing
 */
function processCanvasAssignments($baseUrl, $accessToken, $skywardData, $db, $siteID, $currentSchoolYearID, $accountId, $skywardSections = [], $gcsConfig = null, $batchId = null)
{
    $totalAssignmentsProcessed = 0;
    $processedCourses = 0;

    // Build term mapping from canvas_term_map.csv data
    $termMapping = buildMapping($skywardData['term_map'] ?? []);
    echo "Built term mapping with " . count($termMapping) . " entries\n";
    echo "Getting Canvas courses for mapped terms from API for assignment processing...\n";
    
    // Get Canvas courses for terms specified in term mapping
    $allCourses = getCanvasCoursesForMappedTerms($baseUrl, $accessToken, $accountId, $termMapping);
    if (empty($allCourses)) {
        echo "No Canvas courses found for mapped terms assignment processing\n";
        return ['inserted' => $totalAssignmentsProcessed, 'data' => []];
    }
    
    // Extract course IDs from Canvas API data
    $courseIds = [];
    foreach ($allCourses as $course) {
        if (isset($course['id']) && !in_array($course['id'], $courseIds)) {
            $courseIds[] = $course['id'];
        }
    }

    // Filter courses to only process those that have matching Skyward sections
    $filteredCourseIds = [];
    foreach ($courseIds as $courseId) {
        // Get course info to match with Skyward sections
        foreach ($allCourses as $course) {
            if ($course['id'] == $courseId) {
                $courseTermId = $course['enrollment_term_id'] ?? null;
                $courseTermCode = '';
                if ($courseTermId && isset($termMapping[$courseTermId])) {
                    $courseTermCode = $termMapping[$courseTermId]['term_code'];
                }

                // Check if this course has matching Skyward sections
                $matchingSections = [];
                foreach ($skywardSections as $skywardSection) {
                    // Match by term code
                    if ($skywardSection['term_code'] === $courseTermCode) {
                        $matchingSections[] = $skywardSection;
                    }
                }

                if (!empty($matchingSections)) {
                    $filteredCourseIds[] = $courseId;
                }
                break;
            }
        }
    }

    echo "Filtered to " . count($filteredCourseIds) . " courses that have matching Skyward sections (from " . count($courseIds) . " total courses)\n";

    if (empty($filteredCourseIds)) {
        echo "No courses match Skyward sections - skipping assignment processing\n";
        return ['inserted' => $totalAssignmentsProcessed, 'data' => []];
    }

    echo "Starting PARALLEL assignment+submission processing for " . count($filteredCourseIds) . " filtered courses...\n";

    // Update courseIds to use filtered list
    $courseIds = $filteredCourseIds;

    // Create section metadata mapping from the passed Skyward sections
    $sectionMetadata = [];
    if (!empty($skywardSections)) {
        foreach ($skywardSections as $sectionCode => $sectionData) {
            $sectionMetadata[$sectionCode] = [
                'school_code' => $sectionData['school_code'],
                'staff_id'    => $sectionData['staff_id'],
                'term_code'   => $sectionData['term_code'],
                'period'      => $sectionData['period']
            ];
        }
        echo "Created metadata mapping for " . count($sectionMetadata) . " Skyward sections\n";
    }

    // PARALLEL BATCH FETCHING - Get all data upfront for filtered courses only
    echo "PARALLEL: Batch fetching assignments for " . count($courseIds) . " filtered courses...\n";
    $allAssignments = batchFetchAssignments($baseUrl, $courseIds);
    echo "Fetched assignments for " . count($allAssignments) . " courses\n";

    echo "PARALLEL: Batch fetching submissions for " . count($courseIds) . " filtered courses...\n";
    $allSubmissions = batchFetchSubmissions($baseUrl, $courseIds);
    echo "Fetched submissions for " . count($allSubmissions) . " courses\n";

    // Process assignments in batches to manage memory
    $assignmentBatch = [];
    $batchSize = 50000;
    $adaptiveBatchSize = true;

    echo "Processing assignments with pre-fetched data...\n";
    foreach ($courseIds as $courseId) {
        $processedCourses++;

        // Only log every 50th course to reduce noise
        if ($processedCourses % 50 === 0 || $processedCourses <= 5) {
            echo "Processing course $processedCourses: $courseId\n";
        }

        // Get course term from the Canvas course data
        $courseTermId = null;
        foreach ($allCourses as $course) {
            if ($course['id'] == $courseId) {
                $courseTermId = $course['enrollment_term_id'] ?? null;
                break;
            }
        }

        // Get term code from mapping for this course
        $courseTermCode = '';
        if ($courseTermId && isset($termMapping[$courseTermId])) {
            $courseTermCode = $termMapping[$courseTermId]['term_code'];
        }

        // Find matching Skyward section for this course
        $matchedSection = null;
        $matchedSections = [];

        foreach ($skywardSections as $skywardSection) {
            // Match by term code first
            if ($skywardSection['term_code'] === $courseTermCode) {
                $matchedSections[] = $skywardSection;
            }
        }

        // If we have multiple matches, find the best one
        if (count($matchedSections) === 1) {
            $matchedSection = $matchedSections[0];
        } elseif (count($matchedSections) > 1) {
            $matchedSection = $matchedSections[0];
            echo "Multiple Skyward sections match course $courseId (term: $courseTermCode) - using first match: " . $matchedSection['section_code'] . "\n";
        }

        // Use matched Skyward section data or fallback to course ID
        if ($matchedSection) {
            $skywardSectionCode = $matchedSection['section_code'];
            $sectionMetadataForCourse = [
                'school_code' => $matchedSection['school_code'],
                'staff_id'    => $matchedSection['staff_id'],
                'term_code'   => $matchedSection['term_code'],
                'period'      => $matchedSection['period']
            ];
        } else {
            // Fallback to course ID if no Skyward match found
            $skywardSectionCode = $courseId;
            $sectionMetadataForCourse = [
                'school_code' => '',
                'staff_id'    => '',
                'term_code'   => $courseTermCode,
                'period'      => ''
            ];
        }

        // Get pre-fetched data (no API calls needed!)
        $assignments = $allAssignments[$courseId] ?? [];
        $submissions = $allSubmissions[$courseId] ?? [];

        if (empty($assignments)) {
            continue;
        }

        // Process submissions to create assignment records with student data
        $submissionsByAssignment = [];
        foreach ($submissions as $submission) {
            $assignmentId = $submission['assignment_id'];
            if (!isset($submissionsByAssignment[$assignmentId])) {
                $submissionsByAssignment[$assignmentId] = [];
            }
            $submissionsByAssignment[$assignmentId][] = $submission;
        }

        // Process each assignment and its submissions
        foreach ($assignments as $assignment) {
            $assignmentId = $assignment['id'];
            $assignmentSubmissions = $submissionsByAssignment[$assignmentId] ?? [];

            // Get assignment group info (simplified - no separate API call needed)
            $categoryName = $assignment['assignment_group']['name'] ?? '';

            // Get term code from mapping if available
            $mappedTermCode = '';
            if ($sectionMetadataForCourse && !empty($sectionMetadataForCourse['term_code'])) {
                $mappedTermCode = $sectionMetadataForCourse['term_code'];
            }

            // If no submissions, create one assignment record without student data
            if (empty($assignmentSubmissions)) {
                    $assignmentData = [
                        'assignment_id'    => $assignmentId,
                        'course_id'        => $courseId,
                        'section_id'       => $skywardSectionCode,
                        'school_code'      => $sectionMetadataForCourse ? $sectionMetadataForCourse['school_code'] : '',
                        'staff_id'         => $sectionMetadataForCourse ? $sectionMetadataForCourse['staff_id'] : '',
                        'term_code'        => $mappedTermCode,
                        'period'           => $sectionMetadataForCourse ? $sectionMetadataForCourse['period'] : '',
                        'title'            => removeEmojis($assignment['name'] ?? ''),
                        'description'      => substr(strip_tags(removeEmojis($assignment['description'] ?? '')), 0, 500),
                        'due_date'         => $assignment['due_at'] ?? null,
                        'points_possible'  => $assignment['points_possible'] ?? 0,
                        'assignment_group' => $categoryName,
                        'earned_points'    => null,
                        'published'        => $assignment['published'] ? '1' : '0',
                        'student_id'       => '',
                        'comment'          => ''
                    ];
                    $assignmentBatch[] = $assignmentData;
            } else {
                // Create one record per student submission
                foreach ($assignmentSubmissions as $submission) {
                    // Get student ID from submission using proper integration ID
                    $studentId = getStudentIntegrationId($submission);

                    // Get comment from submission
                    $comment = '';
                    if (!empty($submission['submission_comments'])) {
                        $latestComment = end($submission['submission_comments']);
                        $comment = substr($latestComment['comment'] ?? '', 0, 500);
                    }

                    $assignmentData = [
                        'assignment_id'    => $assignmentId,
                        'course_id'        => $courseId,
                        'section_id'       => $skywardSectionCode,
                        'school_code'      => $sectionMetadataForCourse ? $sectionMetadataForCourse['school_code'] : '',
                        'staff_id'         => $sectionMetadataForCourse ? $sectionMetadataForCourse['staff_id'] : '',
                        'term_code'        => $mappedTermCode,
                        'period'           => $sectionMetadataForCourse ? $sectionMetadataForCourse['period'] : '',
                        'title'            => removeEmojis($assignment['name'] ?? ''),
                        'description'      => substr(strip_tags(removeEmojis($assignment['description'] ?? '')), 0, 500),
                        'due_date'         => $assignment['due_at'] ?? null,
                        'points_possible'  => $assignment['points_possible'] ?? 0,
                        'assignment_group' => $categoryName,
                        'earned_points'    => $submission['score'] ?? null,
                        'published'        => $assignment['published'] ? '1' : '0',
                        'student_id'       => $studentId,
                        'comment'          => removeEmojis($comment)
                    ];
                    $assignmentBatch[] = $assignmentData;
                }
            }

            // Insert batch when it reaches the limit (adaptive batching)
            $shouldInsertBatch = count($assignmentBatch) >= $batchSize;

            // Adaptive batching - insert early if memory usage is high
            if ($adaptiveBatchSize) {
                $memoryUsage = memory_get_usage(true) / 1024 / 1024;
                if ($memoryUsage > 3500) {
                    $shouldInsertBatch = true;
                    echo "Memory pressure detected: {$memoryUsage}MB - inserting batch early\n";
                }
            }

            if ($shouldInsertBatch) {
                logMemoryUsage("Before batch insertion");
                $assignmentsInserted = insertAssignmentsBatch($db, $assignmentBatch, $siteID, $currentSchoolYearID, $gcsConfig, $batchId);
                $totalAssignmentsProcessed += $assignmentsInserted;
                $assignmentBatch = [];

                // Force garbage collection
                if (function_exists('gc_collect_cycles')) {
                    gc_collect_cycles();
                }
                logMemoryUsage("After batch insertion and GC");
            }
        }

        // Periodic memory cleanup every 25 courses
        if ($processedCourses % 25 === 0) {
            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }
            logMemoryUsage("Periodic cleanup after $processedCourses courses");
        }
    }

    // Insert any remaining assignments
    if (!empty($assignmentBatch)) {
        $assignmentsInserted = insertAssignmentsBatch($db, $assignmentBatch, $siteID, $currentSchoolYearID, $gcsConfig, $batchId);
        $totalAssignmentsProcessed += $assignmentsInserted;
        echo "Inserted final batch of " . count($assignmentBatch) . " assignments\n";
    }

    echo "PARALLEL assignment processing complete: $totalAssignmentsProcessed total assignments inserted\n";
    echo "Summary: Processed $processedCourses courses, inserted $totalAssignmentsProcessed assignments\n";

    // Final memory cleanup
    $assignmentBatch = null;
    $allSubmissions = null;
    unset($assignmentBatch, $allSubmissions);

    // Final garbage collection
    if (function_exists('gc_collect_cycles')) {
        gc_collect_cycles();
    }
    logMemoryUsage("Final cleanup after assignment processing");

    return [
        'inserted' => $totalAssignmentsProcessed
    ];
}

/**
 * Merge Skyward and Canvas grades (Skyward takes priority, Canvas fills gaps)
 */
function mergeGradeData($skywardGrades, $canvasGrades)
{
    $mergedGrades = [];
    $processedKeys = [];

    echo "Merging " . count($skywardGrades) . " Skyward grades with " . count($canvasGrades) . " Canvas grades...\n";

    // Create lookup hash for Canvas grades to speed up matching
    echo "Creating Canvas grade lookup hash...\n";
    $canvasLookup = [];
    foreach ($canvasGrades as $canvasGrade) {
        $key = $canvasGrade['student_id'] . '|' . $canvasGrade['section_id'] . '|' . ($canvasGrade['term_code'] ?? '');
        $canvasLookup[$key] = $canvasGrade;
    }
    echo "Created lookup for " . count($canvasLookup) . " Canvas grades\n";

    // Process Skyward grades first (they take priority)
    $skywardCount = 0;
    foreach ($skywardGrades as $skywardGrade) {
        $skywardCount++;

        // Progress logging every 1000 records
        if ($skywardCount % 1000 === 0) {
            echo "Processed $skywardCount Skyward grades...\n";
        }
        // Skip rows with insufficient columns
        if (!is_array($skywardGrade) || count($skywardGrade) < 6) {
            continue;
        }

        $studentId = isset($skywardGrade[0]) ? $skywardGrade[0] : '';
        $sectionCode = isset($skywardGrade[2]) ? $skywardGrade[2] : '';
        $termCode = isset($skywardGrade[5]) ? $skywardGrade[5] : '';
        
        // Create unique key for student+section+term combination
        $key = $studentId . '|' . $sectionCode . '|' . $termCode;
        $processedKeys[$key] = true;

        // Use Skyward data, but fill in missing grades with Canvas if available
        $letterGrade = isset($skywardGrade[9]) ? $skywardGrade[9] : '';
        $percentage = isset($skywardGrade[10]) ? $skywardGrade[10] : '';
        
        // If Skyward grade is missing, try to fill from Canvas
        if (empty($letterGrade) || empty($percentage)) {
            $lookupKey = $studentId . '|' . $sectionCode . '|' . $termCode;
            $canvasMatch = isset($canvasLookup[$lookupKey]) ? $canvasLookup[$lookupKey] : null;
            if ($canvasMatch) {
                $letterGrade = $letterGrade ?: $canvasMatch['letter_grade'];
                $percentage = $percentage ?: $canvasMatch['grade'];
            }
        }

        $mergedGrades[] = [
            'student_id'   => $studentId,
            'course_code'  => isset($skywardGrade[1]) ? $skywardGrade[1] : '',
            'section_code' => $sectionCode,
            'school_code'  => isset($skywardGrade[3]) ? $skywardGrade[3] : '',
            'staff_id'     => isset($skywardGrade[4]) ? $skywardGrade[4] : '',
            'term_code'    => $termCode,
            'period'       => isset($skywardGrade[6]) ? $skywardGrade[6] : '',
            'class_name'   => isset($skywardGrade[7]) ? $skywardGrade[7] : '',
            'teacher_name' => isset($skywardGrade[8]) ? $skywardGrade[8] : '',
            'letter_grade' => $letterGrade,
            'percentage'   => $percentage,
            'performance'  => isset($skywardGrade[11]) ? $skywardGrade[11] : '',
            'source'       => 'skyward' . (($letterGrade !== (isset($skywardGrade[9]) ? $skywardGrade[9] : '') || $percentage !== (isset($skywardGrade[10]) ? $skywardGrade[10] : '')) ? '+canvas' : '')
        ];
    }

    // Add Canvas grades for student+section+term combinations not in Skyward
    foreach ($canvasGrades as $canvasGrade) {
        $studentId = $canvasGrade['student_id'];
        $sectionCode = $canvasGrade['section_id'];
        $termCode = $canvasGrade['term_code'] ?? '';
        
        $key = $studentId . '|' . $sectionCode . '|' . $termCode;
        
        // Only add if this combination wasn't processed from Skyward
        if (!isset($processedKeys[$key])) {
            $mergedGrades[] = [
                'student_id'   => $studentId,
                'course_code'  => $canvasGrade['course_id'] ?? '',
                'section_code' => $sectionCode,
                'school_code'  => '',
                'staff_id'     => '',
                'term_code'    => $termCode,
                'period'       => '',
                'class_name'   => '',
                'teacher_name' => '',
                'letter_grade' => $canvasGrade['letter_grade'],
                'percentage'   => $canvasGrade['grade'],
                'performance'  => '',
                'source'       => 'canvas'
            ];
        }
    }

    echo "Grade merge complete: " . count($mergedGrades) . " total merged grades\n";

    // Clean up memory
    $canvasLookup = null;
    unset($canvasLookup);

    return $mergedGrades;
}


/**
 * Batch insert grades into database using parameterized queries and upload to GCS
 */
function insertGradesBatch($db, $grades, $siteID, $currentSchoolYearID, $gcsConfig = null, $batchId = null)
{
    if (empty($grades)) {
        return 0;
    }

    $rowCounter = 0;
    $batchSize = MAX_IMPORT_LIMIT;

    // Prepare the INSERT statement with placeholders and duplicate handling
    $sql = "INSERT IGNORE INTO abre_grades (
        student_id, course_code, section_code, school_code,
        staff_id, term_code, period, class_name, teacher_name, letter_grade,
        percentage, performance, site_id, school_year_id
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    $stmt = $db->stmt_init();

    if (!$stmt->prepare($sql)) {
        echo "Failed to prepare grade insert statement: " . $stmt->error . "\n";
        return 0;
    }

    // Initialize variables for binding
    $studentID = '';
    $courseCode = '';
    $sectionCode = '';
    $schoolCode = '';
    $staffID = '';
    $termCode = '';
    $period = '';
    $className = '';
    $teacherName = '';
    $letterGrade = '';
    $percentage = '';
    $performance = '';

    // Bind parameters
    $stmt->bind_param(
        'ssssssssssssii',
        $studentID, $courseCode, $sectionCode, $schoolCode,
        $staffID, $termCode, $period, $className, $teacherName,
        $letterGrade, $percentage, $performance, $siteID, $currentSchoolYearID
    );

    // Process grades in batches
    foreach ($grades as $grade) {
        $rowCounter++;

        // Set parameter values
        $studentID = $grade['student_id'];
        $courseCode = $grade['course_code'];
        $sectionCode = $grade['section_code'];
        $schoolCode = $grade['school_code'];
        $staffID = $grade['staff_id'];
        $termCode = $grade['term_code'];
        $period = $grade['period'];
        $className = $grade['class_name'] ?? '';
        $teacherName = $grade['teacher_name'] ?? '';
        $letterGrade = $grade['letter_grade'];
        $percentage = $grade['percentage'];
        $performance = $grade['performance'];

        // Execute the prepared statement
        if (!$stmt->execute()) {
            // Only log non-duplicate key errors
            if (strpos($stmt->error, 'Duplicate entry') === false) {
                echo "Dedupe grade insert: " . $stmt->error . "\n";
            }
            continue;
        }

        // Log progress every batch
        if ($rowCounter % $batchSize === 0) {
            echo "Inserted batch of $batchSize grades (total: $rowCounter)\n";
        }
    }

    $stmt->close();

    echo "Grade insertion complete: $rowCounter records inserted\n";

    // Upload batch data to GCS immediately if config provided
    if ($gcsConfig && isset($gcsConfig->bucket)) {
        uploadGradesBatchToGCS($grades, $gcsConfig, $siteID, $batchId);
    }

    return $rowCounter;
}

/**
 * Upload grades batch to Google Cloud Storage
 */
function uploadGradesBatchToGCS($grades, $gcsConfig, $siteID, $batchId = null)
{
    try {
        $storage = new StorageClient(['projectId' => $gcsConfig->projectId ?? "abre-production"]);
        $bucketName = $gcsConfig->bucket ?? "prd-landing-zone";
        $currentDate = date("Ymd");
        $bucket = $storage->bucket($bucketName);

        // Encode batch data as JSON
        $gradesJsonEncoded = json_encode($grades);

        // Create unique batch filename
        $batchSuffix = $batchId ? "_batch_$batchId" : '';
        $timestamp = date('His');

        // Upload to site-id folder
        $tempFile1 = tmpfile();
        fwrite($tempFile1, $gradesJsonEncoded);
        rewind($tempFile1);
        $fileName = 'Abre_Canvas_Skyward_grades' . $batchSuffix . '_' . $timestamp . '.json';
        $bucket->upload($tempFile1, [
            'name' => $currentDate . '/site-id/' . $siteID . '/' . $fileName
        ]);
        fclose($tempFile1);

        // Upload to filename folder
        $tempFile2 = tmpfile();
        fwrite($tempFile2, $gradesJsonEncoded);
        rewind($tempFile2);
        $folderName = 'Abre_Canvas_Skyward_grades';
        $bucket->upload($tempFile2, [
            'name' => $currentDate . '/filename/' . $folderName . '/' . $folderName . '-' . $siteID . $batchSuffix . '_' . $timestamp . '.json'
        ]);
        fclose($tempFile2);

        echo "Uploaded grades batch to GCS: " . count($grades) . " records\n";

    } catch (Exception $e) {
        error_log('GCS grades batch upload failed: ' . $e->getMessage());
        // Don't throw exception - GCS upload is optional
    }
}

/**
 * Batch insert assignments into database using parameterized queries and upload to GCS
 */
function insertAssignmentsBatch($db, $assignments, $siteID, $currentSchoolYearID, $gcsConfig = null, $batchId = null)
{
    if (empty($assignments)) {
        echo "No assignments to insert\n";
        return 0;
    }

    echo "Starting assignment database insertion for " . count($assignments) . " assignments...\n";
    
    $rowCounter = 0;
    $batchSize = MAX_IMPORT_LIMIT;

    // Prepare the INSERT statement with placeholders
    $sql = "INSERT IGNORE INTO abre_assignments (
        student_id, course_code, section_code, school_code,
        staff_id, term_code, period, title, description, due_date, category,
        earned_points, possible_points, weight_percentage, comment, published,
        site_id, school_year_id
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    $stmt = $db->stmt_init();

    if (!$stmt->prepare($sql)) {
        echo "Failed to prepare assignment insert statement: " . $stmt->error . "\n";
        return 0;
    }

    // Initialize variables for binding
    $studentID = '';
    $courseCode = '';
    $sectionCode = '';
    $schoolCode = '';
    $staffID = '';
    $termCode = '';
    $period = '';
    $title = '';
    $description = '';
    $dueDate = null;
    $category = '';
    $earnedPoints = null;
    $possiblePoints = '';
    $weightPercentage = null;
    $comment = '';
    $published = '';

    // Bind parameters
    $stmt->bind_param(
        'ssssssssssssssssii',
        $studentID, $courseCode, $sectionCode, $schoolCode,
        $staffID, $termCode, $period, $title, $description, $dueDate,
        $category, $earnedPoints, $possiblePoints, $weightPercentage, $comment, $published,
        $siteID, $currentSchoolYearID
    );

    // Process assignments in batches
    foreach ($assignments as $assignment) {
        $rowCounter++;

        // Set parameter values
        $studentID = $assignment['student_id'] ?? '';
        $courseCode = $assignment['course_id'];
        $sectionCode = $assignment['section_id'];
        $schoolCode = $assignment['school_code'] ?? '';
        $staffID = $assignment['staff_id'] ?? '';
        $termCode = $assignment['term_code'] ?? '';
        $period = $assignment['period'] ?? '';
        $title = $assignment['title'];
        $description = $assignment['description'];
        $dueDate = $assignment['due_date'] ?? null;
        $category = $assignment['assignment_group'];
        $earnedPoints = $assignment['earned_points'] ?? null;
        $possiblePoints = $assignment['points_possible'];
        $weightPercentage = null;
        $comment = $assignment['comment'] ?? '';
        $published = $assignment['published'];

        // Execute the prepared statement
        if (!$stmt->execute()) {
            // Only log non-duplicate key errors
            if (strpos($stmt->error, 'Duplicate entry') === false) {
                echo "Failed to execute assignment insert: " . $stmt->error . "\n";
            }
            continue;
        }

        // Log progress every batch
        if ($rowCounter % $batchSize === 0) {
            echo "Inserted batch of $batchSize assignments (total: $rowCounter)\n";
        }
    }

    $stmt->close();

    echo "Assignment insertion complete: $rowCounter records inserted\n";

    // Upload batch data to GCS immediately if config provided
    if ($gcsConfig && isset($gcsConfig->bucket)) {
        uploadAssignmentsBatchToGCS($assignments, $gcsConfig, $siteID, $batchId);
    }

    return $rowCounter;
}

/**
 * Upload assignments batch to Google Cloud Storage
 */
function uploadAssignmentsBatchToGCS($assignments, $gcsConfig, $siteID, $batchId = null)
{
    try {
        $storage = new StorageClient(['projectId' => $gcsConfig->projectId ?? "abre-production"]);
        $bucketName = $gcsConfig->bucket ?? "prd-landing-zone";
        $currentDate = date("Ymd");
        $bucket = $storage->bucket($bucketName);

        // Encode batch data as JSON
        $assignmentsJsonEncoded = json_encode($assignments);

        // Create unique batch filename
        $batchSuffix = $batchId ? "_batch_$batchId" : '';
        $timestamp = date('His');

        // Upload to site-id folder
        $tempFile1 = tmpfile();
        fwrite($tempFile1, $assignmentsJsonEncoded);
        rewind($tempFile1);
        $fileName = 'Abre_Canvas_Skyward_assignments' . $batchSuffix . '_' . $timestamp . '.json';
        $bucket->upload($tempFile1, [
            'name' => $currentDate . '/site-id/' . $siteID . '/' . $fileName
        ]);
        fclose($tempFile1);

        // Upload to filename folder
        $tempFile2 = tmpfile();
        fwrite($tempFile2, $assignmentsJsonEncoded);
        rewind($tempFile2);
        $folderName = 'Abre_Canvas_Skyward_assignments';
        $bucket->upload($tempFile2, [
            'name' => $currentDate . '/filename/' . $folderName . '/' . $folderName . '-' . $siteID . $batchSuffix . '_' . $timestamp . '.json'
        ]);
        fclose($tempFile2);

        echo "Uploaded assignments batch to GCS: " . count($assignments) . " records\n";

    } catch (Exception $e) {
        error_log('GCS assignments batch upload failed: ' . $e->getMessage());
        // Don't throw exception - GCS upload is optional
    }
}

/**
 * Batch fetch enrollments for multiple courses
 */
function batchFetchEnrollments($canvasClient, $courseIds)
{
    $allEnrollments = [];
    $totalCourses = count($courseIds);

    echo "Processing $totalCourses courses for enrollments...\n";

    foreach ($courseIds as $courseId) {
        $result = $canvasClient->getAllPaginatedData("/courses/$courseId/enrollments", [
            'type[]'    => 'StudentEnrollment',
            'include[]' => 'grades',
            'per_page'  => 100
        ]);

        if ($result !== false) {
            $allEnrollments[$courseId] = $result;
        } else {
            echo "Failed to fetch enrollments for course $courseId\n";
            $allEnrollments[$courseId] = [];
        }

        // No artificial delay needed - let Canvas API rate limits govern speed
    }

    echo "Batch fetch complete! Got data for " . count($allEnrollments) . " courses\n";
    return $allEnrollments;
}

/**
 * Batch fetch submissions for multiple courses with TRUE parallel processing
 */
function batchFetchSubmissions($baseUrl, $courseIds)
{
    $allSubmissions = [];
    $batchSize = 35;
    $totalCourses = count($courseIds);
    
    echo "Processing $totalCourses courses for submissions in PARALLEL batches of $batchSize...\n";
    
    for ($i = 0; $i < $totalCourses; $i += $batchSize) {
        $batch = array_slice($courseIds, $i, $batchSize);
        $batchNum = floor($i / $batchSize) + 1;
        $totalBatches = ceil($totalCourses / $batchSize);
        
        echo "Processing PARALLEL submissions batch $batchNum/$totalBatches (" . count($batch) . " courses)...\n";
        
        // Use cURL multi-handle for TRUE parallel processing
        $multiHandle = curl_multi_init();
        $curlHandles = [];
        $courseData = [];
        
        // Initialize all courses in the batch with first page
        foreach ($batch as $courseId) {
            $courseData[$courseId] = [
                'submissions' => [],
                'page'        => 1,
                'hasMore'     => true,
                'success'     => true
            ];
            
            $url = $baseUrl . '/api/v1/courses/' . $courseId . '/students/submissions?' . http_build_query([
                'student_ids[]' => 'all',
                'include[]'     => ['assignment', 'submission_comments', 'user'],
                'per_page'      => 100,
                'page'          => 1
            ]);
            
            $currentToken = getCurrentCanvasAccessToken();
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $currentToken,
                'Accept: application/json',
                'Connection: keep-alive'
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 60);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
            curl_setopt($ch, CURLOPT_TCP_KEEPALIVE, 1);
            curl_setopt($ch, CURLOPT_TCP_KEEPIDLE, 10);
            curl_setopt($ch, CURLOPT_TCP_KEEPINTVL, 5);
            
            curl_multi_add_handle($multiHandle, $ch);
            $curlHandles[$courseId] = $ch;
        }
        
        // Execute all requests in parallel
        $running = null;
        do {
            curl_multi_exec($multiHandle, $running);
            curl_multi_select($multiHandle);
        } while ($running > 0);
        
        // Process results and handle pagination
        $batchStats = [
            'courses_processed'        => 0,
            'total_submissions'        => 0,
            'courses_with_submissions' => 0,
            'failed_courses'           => 0
        ];
        
        foreach ($curlHandles as $courseId => $ch) {
            $response = curl_multi_getcontent($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            if ($httpCode === 200 && $response) {
                $data = json_decode($response, true);
                if (is_array($data) && !empty($data)) {
                    $courseData[$courseId]['submissions'] = array_merge($courseData[$courseId]['submissions'], $data);
                    $courseData[$courseId]['page']++;
                    $courseData[$courseId]['hasMore'] = count($data) === 100;
                } else {
                    $courseData[$courseId]['hasMore'] = false;
                }
            } else if ($httpCode === 401) {
                echo "401 Unauthorized for course $courseId - attempting to refresh token...\n";
                
                // Try to refresh the token
                if (isset($GLOBALS['canvas_config'])) {
                    $newToken = getCanvasAccessToken($GLOBALS['canvas_config']);
                    if ($newToken) {
                        echo "Token refreshed successfully, retrying course $courseId...\n";
                        $GLOBALS['canvas_access_token'] = $newToken;
                        // Update the CanvasAPIClient object with the new token
                        if (isset($GLOBALS['canvas_client'])) {
                            $GLOBALS['canvas_client']->updateAccessToken($newToken);
                        }
                        
                        // Retry the request with the new token
                        $url = $baseUrl . '/api/v1/courses/' . $courseId . '/students/submissions?' . http_build_query([
                            'student_ids[]' => 'all',
                            'include[]'     => ['assignment', 'submission_comments', 'user'],
                            'per_page'      => 100,
                            'page'          => 1
                        ]);
                        
                        $retryCh = curl_init();
                        curl_setopt($retryCh, CURLOPT_URL, $url);
                        curl_setopt($retryCh, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($retryCh, CURLOPT_HTTPHEADER, [
                            'Authorization: Bearer ' . $newToken,
                            'Accept: application/json',
                            'Connection: keep-alive'
                        ]);
                        curl_setopt($retryCh, CURLOPT_TIMEOUT, 60);
                        curl_setopt($retryCh, CURLOPT_SSL_VERIFYPEER, true);
                        curl_setopt($retryCh, CURLOPT_TCP_KEEPALIVE, 1);
                        curl_setopt($retryCh, CURLOPT_TCP_KEEPIDLE, 10);
                        curl_setopt($retryCh, CURLOPT_TCP_KEEPINTVL, 5);
                        
                        $retryResponse = curl_exec($retryCh);
                        $retryHttpCode = curl_getinfo($retryCh, CURLINFO_HTTP_CODE);
                        curl_close($retryCh);
                        
                        if ($retryHttpCode === 200 && $retryResponse) {
                            $retryData = json_decode($retryResponse, true);
                            if (is_array($retryData) && !empty($retryData)) {
                                $courseData[$courseId]['submissions'] = array_merge($courseData[$courseId]['submissions'], $retryData);
                                $courseData[$courseId]['page']++;
                                $courseData[$courseId]['hasMore'] = count($retryData) === 100;
                            } else {
                                $courseData[$courseId]['hasMore'] = false;
                            }
                        } else {
                            echo "Failed to fetch submissions for course $courseId after token refresh (HTTP $retryHttpCode)\n";
                            $courseData[$courseId]['success'] = false;
                            $courseData[$courseId]['hasMore'] = false;
                        }
                    } else {
                        echo "Failed to refresh token for course $courseId\n";
                        $courseData[$courseId]['success'] = false;
                        $courseData[$courseId]['hasMore'] = false;
                    }
                } else {
                    echo "No Canvas config available for token refresh for course $courseId\n";
                    $courseData[$courseId]['success'] = false;
                    $courseData[$courseId]['hasMore'] = false;
                }
            } else {
                echo "Failed to fetch submissions for course $courseId (HTTP $httpCode)\n";
                $courseData[$courseId]['success'] = false;
                $courseData[$courseId]['hasMore'] = false;
            }
            
            curl_multi_remove_handle($multiHandle, $ch);
            curl_close($ch);
        }
        
        curl_multi_close($multiHandle);
        
        // Handle pagination for courses that need more data (optimized parallel approach)
        $coursesNeedingPagination = array_filter($courseData, function($data) {
            return $data['hasMore'] && $data['success'];
        });

        if (!empty($coursesNeedingPagination)) {
            echo "Handling pagination for " . count($coursesNeedingPagination) . " courses...\n";

            // Process pagination in smaller parallel batches to avoid overwhelming the API
            $paginationBatchSize = 12;
            $paginationCourses = array_keys($coursesNeedingPagination);

            for ($p = 0; $p < count($paginationCourses); $p += $paginationBatchSize) {
                $paginationBatch = array_slice($paginationCourses, $p, $paginationBatchSize);

                // Collect all pagination requests for this batch
                $paginationRequests = [];
                foreach ($paginationBatch as $courseId) {
                    $page = $courseData[$courseId]['page'];
                    $url = $baseUrl . '/api/v1/courses/' . $courseId . '/students/submissions?' . http_build_query([
                        'student_ids[]' => 'all',
                        'include[]'     => ['assignment', 'submission_comments', 'user'],
                        'per_page'      => 100,
                        'page'          => $page
                    ]);

                    $paginationRequests[$courseId] = [
                        'url' => $url,
                        'page' => $page
                    ];
                }

                // Execute pagination requests in parallel
                $paginationMultiHandle = curl_multi_init();
                $paginationHandles = [];

                foreach ($paginationRequests as $courseId => $request) {
                    $currentToken = getCurrentCanvasAccessToken();

                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $request['url']);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_HTTPHEADER, [
                        'Authorization: Bearer ' . $currentToken,
                        'Accept: application/json',
                        'Connection: keep-alive'
                    ]);
                    curl_setopt($ch, CURLOPT_TIMEOUT, 60);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
                    curl_setopt($ch, CURLOPT_TCP_KEEPALIVE, 1);
                    curl_setopt($ch, CURLOPT_TCP_KEEPIDLE, 10);
                    curl_setopt($ch, CURLOPT_TCP_KEEPINTVL, 5);

                    curl_multi_add_handle($paginationMultiHandle, $ch);
                    $paginationHandles[$courseId] = $ch;
                }

                // Execute pagination requests
                $running = null;
                do {
                    curl_multi_exec($paginationMultiHandle, $running);
                    curl_multi_select($paginationMultiHandle);
                } while ($running > 0);

                // Process pagination results
                foreach ($paginationHandles as $courseId => $ch) {
                    $response = curl_multi_getcontent($ch);
                    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

                    if ($httpCode === 200 && $response) {
                        $pageData = json_decode($response, true);
                        if (is_array($pageData) && !empty($pageData)) {
                            $courseData[$courseId]['submissions'] = array_merge($courseData[$courseId]['submissions'], $pageData);
                            $courseData[$courseId]['page']++;
                            $courseData[$courseId]['hasMore'] = count($pageData) === 100;
                        } else {
                            $courseData[$courseId]['hasMore'] = false;
                        }
                    } else {
                        $courseData[$courseId]['hasMore'] = false;
                    }

                    curl_multi_remove_handle($paginationMultiHandle, $ch);
                    curl_close($ch);
                }

                curl_multi_close($paginationMultiHandle);
            }
        }
        
        // Store results and update statistics
        foreach ($courseData as $courseId => $data) {
            $allSubmissions[$courseId] = $data['submissions'];
            $batchStats['courses_processed']++;
            
            if ($data['success']) {
                $batchStats['total_submissions'] += count($data['submissions']);
                if (count($data['submissions']) > 0) {
                    $batchStats['courses_with_submissions']++;
                }
            } else {
                $batchStats['failed_courses']++;
            }
        }
        
        // Show batch summary
        echo "PARALLEL batch $batchNum complete: {$batchStats['courses_processed']} courses, {$batchStats['total_submissions']} submissions, {$batchStats['courses_with_submissions']} courses with data";
        if ($batchStats['failed_courses'] > 0) {
            echo ", {$batchStats['failed_courses']} failed";
        }
        echo "\n";

        // No artificial delay needed - let Canvas API rate limits govern speed
    }
    
    echo "PARALLEL submissions fetch complete! Got data for " . count($allSubmissions) . " courses\n";
    return $allSubmissions;
}

/**
 * Batch fetch assignments for multiple courses with full pagination
 */
function batchFetchAssignments($baseUrl, $courseIds)
{
    $allAssignments = [];
    $batchSize = 50;
    $totalCourses = count($courseIds);

    echo "Processing $totalCourses courses for assignments in PARALLEL batches of $batchSize...\n";

    for ($i = 0; $i < $totalCourses; $i += $batchSize) {
        $batch = array_slice($courseIds, $i, $batchSize);
        $batchNum = floor($i / $batchSize) + 1;
        $totalBatches = ceil($totalCourses / $batchSize);

        echo "Processing PARALLEL assignments batch $batchNum/$totalBatches (" . count($batch) . " courses)...\n";

        // Use cURL multi-handle for parallel processing
        $multiHandle = curl_multi_init();
        $curlHandles = [];
        $courseData = [];

        // Initialize all courses in the batch with first page
        foreach ($batch as $courseId) {
            $courseData[$courseId] = [
                'assignments' => [],
                'page'        => 1,
                'hasMore'     => true,
                'success'     => true
            ];

            $url = $baseUrl . '/api/v1/courses/' . $courseId . '/assignments?' . http_build_query([
                'per_page' => 100,
                'include'  => ['all_dates', 'overrides'],
                'page'     => 1
            ]);

            $currentToken = getCurrentCanvasAccessToken();

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HEADER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $currentToken,
                'Accept: application/json',
                'Connection: keep-alive'
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 60);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
            curl_setopt($ch, CURLOPT_TCP_KEEPALIVE, 1);
            curl_setopt($ch, CURLOPT_TCP_KEEPIDLE, 10);
            curl_setopt($ch, CURLOPT_TCP_KEEPINTVL, 5);

            curl_multi_add_handle($multiHandle, $ch);
            $curlHandles[$courseId] = $ch;
        }

        // Execute all requests in parallel
        $running = null;
        do {
            curl_multi_exec($multiHandle, $running);
            curl_multi_select($multiHandle);
        } while ($running > 0);

        // Process results and handle pagination
        $batchStats = [
            'courses_processed'        => 0,
            'total_assignments'        => 0,
            'courses_with_assignments' => 0,
            'failed_courses'           => 0
        ];

        foreach ($curlHandles as $courseId => $ch) {
            $response = curl_multi_getcontent($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            // Get response headers to check for pagination
            $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
            $headers = substr($response, 0, $headerSize);
            $body = substr($response, $headerSize);

            if ($httpCode === 200 && $body) {
                $data = json_decode($body, true);
                if (is_array($data) && !empty($data)) {
                    $courseData[$courseId]['assignments'] = array_merge($courseData[$courseId]['assignments'], $data);
                    $courseData[$courseId]['page']++;
                    $courseData[$courseId]['hasMore'] = count($data) === 100;
                } else {
                    $courseData[$courseId]['hasMore'] = false;
                }
            } else if ($httpCode === 401) {
                echo "401 Unauthorized for course $courseId - attempting to refresh token...\n";

                // Try to refresh the token
                if (isset($GLOBALS['canvas_config'])) {
                    $newToken = getCanvasAccessToken($GLOBALS['canvas_config']);
                    if ($newToken) {
                        echo "Token refreshed successfully, retrying course $courseId...\n";
                        $GLOBALS['canvas_access_token'] = $newToken;
                        // Update the CanvasAPIClient object with the new token
                        if (isset($GLOBALS['canvas_client'])) {
                            $GLOBALS['canvas_client']->updateAccessToken($newToken);
                        }

                        // Retry the request with the new token
                        $url = $baseUrl . '/api/v1/courses/' . $courseId . '/assignments?' . http_build_query([
                            'per_page' => 100,
                            'include'  => ['all_dates', 'overrides'],
                            'page'     => 1
                        ]);

                        $retryCh = curl_init();
                        curl_setopt($retryCh, CURLOPT_URL, $url);
                        curl_setopt($retryCh, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($retryCh, CURLOPT_HEADER, true);
                        curl_setopt($retryCh, CURLOPT_HTTPHEADER, [
                            'Authorization: Bearer ' . $newToken,
                            'Accept: application/json',
                            'Connection: keep-alive'
                        ]);
                        curl_setopt($retryCh, CURLOPT_TIMEOUT, 60);
                        curl_setopt($retryCh, CURLOPT_SSL_VERIFYPEER, true);
                        curl_setopt($retryCh, CURLOPT_TCP_KEEPALIVE, 1);
                        curl_setopt($retryCh, CURLOPT_TCP_KEEPIDLE, 10);
                        curl_setopt($retryCh, CURLOPT_TCP_KEEPINTVL, 5);

                        $retryResponse = curl_exec($retryCh);
                        $retryHttpCode = curl_getinfo($retryCh, CURLINFO_HTTP_CODE);
                        $retryHeaderSize = curl_getinfo($retryCh, CURLINFO_HEADER_SIZE);
                        curl_close($retryCh);

                        if ($retryHttpCode === 200 && $retryResponse) {
                            $retryHeaders = substr($retryResponse, 0, $retryHeaderSize);
                            $retryBody = substr($retryResponse, $retryHeaderSize);

                            $retryData = json_decode($retryBody, true);
                            if (is_array($retryData) && !empty($retryData)) {
                                $courseData[$courseId]['assignments'] = array_merge($courseData[$courseId]['assignments'], $retryData);
                                $courseData[$courseId]['page']++;
                                $courseData[$courseId]['hasMore'] = count($retryData) === 100;
                            } else {
                                $courseData[$courseId]['hasMore'] = false;
                            }
                        } else {
                            echo "Failed to fetch assignments for course $courseId after token refresh (HTTP $retryHttpCode)\n";
                            $courseData[$courseId]['success'] = false;
                            $courseData[$courseId]['hasMore'] = false;
                        }
                    } else {
                        echo "Failed to refresh token for course $courseId\n";
                        $courseData[$courseId]['success'] = false;
                        $courseData[$courseId]['hasMore'] = false;
                    }
                } else {
                    echo "No Canvas config available for token refresh for course $courseId\n";
                    $courseData[$courseId]['success'] = false;
                    $courseData[$courseId]['hasMore'] = false;
                }
            } else {
                echo "Failed to fetch assignments for course $courseId (HTTP $httpCode)\n";
                $courseData[$courseId]['success'] = false;
                $courseData[$courseId]['hasMore'] = false;
            }

            curl_multi_remove_handle($multiHandle, $ch);
            curl_close($ch);
        }

        curl_multi_close($multiHandle);

        // Handle pagination for courses that need more data (optimized parallel approach)
        $coursesNeedingPagination = array_filter($courseData, function($data) {
            return $data['hasMore'] && $data['success'];
        });

        if (!empty($coursesNeedingPagination)) {
            echo "Handling pagination for " . count($coursesNeedingPagination) . " courses...\n";

            // Process pagination in smaller parallel batches to avoid overwhelming the API
            $paginationBatchSize = 15;
            $paginationCourses = array_keys($coursesNeedingPagination);

            for ($p = 0; $p < count($paginationCourses); $p += $paginationBatchSize) {
                $paginationBatch = array_slice($paginationCourses, $p, $paginationBatchSize);

                // Collect all pagination requests for this batch
                $paginationRequests = [];
                foreach ($paginationBatch as $courseId) {
                    $page = $courseData[$courseId]['page'];
                    $url = $baseUrl . '/api/v1/courses/' . $courseId . '/assignments?' . http_build_query([
                        'per_page' => 100,
                        'include'  => ['all_dates', 'overrides'],
                        'page'     => $page
                    ]);

                    $paginationRequests[$courseId] = [
                        'url' => $url,
                        'page' => $page
                    ];
                }

                // Execute pagination requests in parallel
                $paginationMultiHandle = curl_multi_init();
                $paginationHandles = [];

                foreach ($paginationRequests as $courseId => $request) {
                    $currentToken = getCurrentCanvasAccessToken();

                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $request['url']);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_HEADER, true);
                    curl_setopt($ch, CURLOPT_HTTPHEADER, [
                        'Authorization: Bearer ' . $currentToken,
                        'Accept: application/json',
                        'Connection: keep-alive'
                    ]);
                    curl_setopt($ch, CURLOPT_TIMEOUT, 60);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
                    curl_setopt($ch, CURLOPT_TCP_KEEPALIVE, 1);
                    curl_setopt($ch, CURLOPT_TCP_KEEPIDLE, 10);
                    curl_setopt($ch, CURLOPT_TCP_KEEPINTVL, 5);

                    curl_multi_add_handle($paginationMultiHandle, $ch);
                    $paginationHandles[$courseId] = $ch;
                }

                // Execute pagination requests
                $running = null;
                do {
                    curl_multi_exec($paginationMultiHandle, $running);
                    curl_multi_select($paginationMultiHandle);
                } while ($running > 0);

                // Process pagination results
                foreach ($paginationHandles as $courseId => $ch) {
                    $response = curl_multi_getcontent($ch);
                    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

                    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
                    $headers = substr($response, 0, $headerSize);
                    $body = substr($response, $headerSize);

                    if ($httpCode === 200 && $body) {
                        $pageData = json_decode($body, true);
                        if (is_array($pageData) && !empty($pageData)) {
                            $courseData[$courseId]['assignments'] = array_merge($courseData[$courseId]['assignments'], $pageData);
                            $courseData[$courseId]['page']++;
                            $courseData[$courseId]['hasMore'] = count($pageData) === 100;
                        } else {
                            $courseData[$courseId]['hasMore'] = false;
                        }
                    } else {
                        $courseData[$courseId]['hasMore'] = false;
                    }

                    curl_multi_remove_handle($paginationMultiHandle, $ch);
                    curl_close($ch);
                }

                curl_multi_close($paginationMultiHandle);
            }
        }

        // Store results and update statistics
        foreach ($courseData as $courseId => $data) {
            $allAssignments[$courseId] = $data['assignments'];
            $batchStats['courses_processed']++;

            if ($data['success']) {
                $batchStats['total_assignments'] += count($data['assignments']);
                if (count($data['assignments']) > 0) {
                    $batchStats['courses_with_assignments']++;
                }
            } else {
                $batchStats['failed_courses']++;
            }
        }

        // Show batch summary
        echo "PARALLEL batch $batchNum complete: {$batchStats['courses_processed']} courses, {$batchStats['total_assignments']} assignments, {$batchStats['courses_with_assignments']} courses with data";
        if ($batchStats['failed_courses'] > 0) {
            echo ", {$batchStats['failed_courses']} failed";
        }
        echo "\n";
    }

    echo "Batch fetch complete! Got data for " . count($allAssignments) . " courses\n";
    return $allAssignments;
}

/**
 * Remove emojis from text
 */
function removeEmojis($text)
{
    // Remove emoji characters (Unicode ranges)
    $text = preg_replace('/[\x{1F600}-\x{1F64F}]/u', '', $text);
    $text = preg_replace('/[\x{1F300}-\x{1F5FF}]/u', '', $text);
    $text = preg_replace('/[\x{1F680}-\x{1F6FF}]/u', '', $text);
    $text = preg_replace('/[\x{1F1E0}-\x{1F1FF}]/u', '', $text);
    $text = preg_replace('/[\x{2600}-\x{26FF}]/u', '', $text);
    $text = preg_replace('/[\x{2700}-\x{27BF}]/u', '', $text);

    return trim($text);
}

/**
 * Monitor memory usage
 */
function logMemoryUsage($message = "")
{
    $memory = memory_get_usage(true) / 1024 / 1024;
    $peakMemory = memory_get_peak_usage(true) / 1024 / 1024;
    echo "Memory: " . round($memory, 1) . "MB used, " . round($peakMemory, 1) . "MB peak" . ($message ? " - $message" : "") . "\n";
}

/**
 * Filter Skyward grades for specific sections only
 */
function filterSkywardGradesForSections($skywardGrades, $sections)
{
    if (empty($skywardGrades) || empty($sections)) {
        return [];
    }

    $filteredGrades = [];
    $sectionCodes = array_keys($sections);

    foreach ($skywardGrades as $grade) {
        if (!is_array($grade) || count($grade) < 3) {
            continue;
        }

        $sectionCode = isset($grade[2]) ? $grade[2] : '';
        if (in_array($sectionCode, $sectionCodes)) {
            $filteredGrades[] = $grade;
        }
    }

    return $filteredGrades;
}

/**
 * Clean up old cache files
 */
function cleanupCache()
{
    $cacheDir = '/tmp/canvas_cache/';
    if (is_dir($cacheDir)) {
        $files = glob($cacheDir . '*.json');
        $cleaned = 0;
        foreach ($files as $file) {
            if (time() - filemtime($file) > 86400) {
                unlink($file);
                $cleaned++;
            }
        }
        if ($cleaned > 0) {
            echo "Cleaned up $cleaned old cache files\n";
        }
    }
}
