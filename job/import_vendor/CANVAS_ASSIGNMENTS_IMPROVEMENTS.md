# Canvas Assignments Processing Improvements

## Issues Identified

Based on your analysis, the Canvas assignment processing had several critical problems:

### 1. **No student_id in assignments**
- **Problem**: The original code set `student_id` to empty string for all assignment records
- **Root Cause**: Processing assignments at course level instead of submission level
- **Impact**: Unable to track which student completed which assignment

### 2. **Course and section code confusion**
- **Problem**: Using Canvas Course ID for both `course_id` and `section_id` fields
- **Root Cause**: Misunderstanding of Canvas data structure vs Skyward expectations
- **Impact**: Incorrect section mapping in Skyward

### 3. **School codes all the same**
- **Problem**: All assignments getting the same school code
- **Root Cause**: Using fallback metadata when section matching fails
- **Impact**: Incorrect school attribution

### 4. **Missing assignments (80%)**
- **Problem**: Only fetching course-level assignments, not individual student submissions
- **Root Cause**: Not following the proper Canvas API flow for assignments + submissions
- **Impact**: Massive data loss - most assignment work not captured

## Solution Implemented

### New Canvas API Flow

Following your recommended sequence:

1. **POST /login/oauth2/token** ✅ (existing)
2. **GET /api/v1/accounts** ✅ (existing)
3. **GET /api/v1/accounts/{account_id}/terms** ✅ (existing)
4. **GET /api/v1/accounts/{account_id}/courses** ✅ (existing)
5. **GET /api/v1/courses/{course_id}/sections** ✅ (existing)
6. **GET /api/v1/courses/{course_id}/assignments** ✅ (improved)
7. **GET /api/v1/courses/{course_id}/assignment_groups** ✅ (added)
8. **GET /api/v1/courses/{course_id}/students/submissions** ✅ (improved with user data)

### Key Improvements Made

#### 1. **Proper Student ID Handling**
```php
// NEW: Get student integration ID from submission user data
function getStudentIntegrationId($submission)
{
    // Try integration_id first (preferred)
    if (isset($submission['user']['integration_id']) && !empty($submission['user']['integration_id'])) {
        return $submission['user']['integration_id'];
    }
    
    // Fallback to sis_user_id
    if (isset($submission['user']['sis_user_id']) && !empty($submission['user']['sis_user_id'])) {
        return $submission['user']['sis_user_id'];
    }
    
    // Last resort: Canvas user ID
    return $submission['user_id'] ?? '';
}
```

#### 2. **Submissions-Based Processing**
```php
// NEW: Process submissions to create student-level assignment records
foreach ($assignmentSubmissions as $submission) {
    $studentId = getStudentIntegrationId($submission);
    
    $assignmentBatch[] = [
        'assignment_id'    => $assignmentId,
        'course_id'        => $courseId,
        'section_id'       => $skywardSectionCode, // Use actual Skyward section code
        'school_code'      => $sectionMetadataForCourse['school_code'],
        'staff_id'         => $sectionMetadataForCourse['staff_id'],
        'term_code'        => $mappedTermCode,
        'period'           => $sectionMetadataForCourse['period'],
        'student_id'       => $studentId, // NOW PROPERLY POPULATED
        'earned_points'    => $submission['score'] ?? null, // ACTUAL STUDENT SCORE
        'comment'          => $comment // SUBMISSION COMMENTS
    ];
}
```

#### 3. **Enhanced API Calls**
```php
// IMPROVED: Include user data in submissions
$params = [
    'student_ids[]' => 'all',
    'include[]'     => ['assignment', 'submission_comments', 'user'], // Added 'user'
    'per_page'      => 100
];
```

#### 4. **Proper Section Code Mapping**
```php
// FIXED: Use actual Skyward section code instead of Canvas course ID
'section_id' => $skywardSectionCode, // Not $courseId
```

#### 5. **Assignment Group Integration**
```php
// NEW: Get assignment groups for proper categorization
$assignmentGroups = getCanvasAssignmentGroups($baseUrl, $accessToken, $courseId);
$groupLookup = [];
foreach ($assignmentGroups as $group) {
    $groupLookup[$group['id']] = $group;
}

// Use in assignment processing
$assignmentGroup = $groupLookup[$assignment['assignment_group_id']] ?? null;
$categoryName = $assignmentGroup ? $assignmentGroup['name'] : '';
```

## Testing

### Test Script Created
- `testCanvasAssignments.php` - Helps debug the assignment processing
- Tests all API calls for a sample course
- Analyzes submission data quality
- Identifies potential issues

### Run the test:
```bash
cd /Users/<USER>/Sites/Abre-Cron/job/import_vendor
php testCanvasAssignments.php
```

## Expected Results

### Before (Issues):
- ❌ `student_id`: Empty for all records
- ❌ `section_id`: Canvas Course ID (wrong)
- ❌ `school_code`: Same for all records
- ❌ `earned_points`: Empty (no submission data)
- ❌ Coverage: ~20% of assignments (course-level only)

### After (Fixed):
- ✅ `student_id`: Proper integration_id from Canvas user data
- ✅ `section_id`: Actual Skyward section code
- ✅ `school_code`: Correct per section metadata
- ✅ `earned_points`: Actual student scores from submissions
- ✅ Coverage: ~100% of assignments (submission-level)

## Next Steps

1. **Test the improvements**:
   ```bash
   php testCanvasAssignments.php
   ```

2. **Run the full import**:
   ```bash
   php localCanvas.php
   ```

3. **Verify database results**:
   - Check `abre_assignments` table for proper `student_id` population
   - Verify `section_id` matches Skyward section codes
   - Confirm `earned_points` has actual scores
   - Validate assignment count increase

4. **Monitor performance**:
   - The new approach fetches more data (submissions + user info)
   - Memory usage may be higher but should be manageable with batching
   - Processing time will increase but data quality will be much better

## API Rate Limiting

The improved approach makes more API calls:
- **Before**: ~2 calls per course (assignments + assignment_groups)
- **After**: ~3 calls per course (assignments + assignment_groups + submissions)

With 50ms delays between requests, this should stay well within Canvas rate limits (3000/hour).
