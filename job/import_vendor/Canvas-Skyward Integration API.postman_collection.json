{"info": {"_postman_id": "881d51ab-5d9b-412f-8f97-bd53f63de90a", "name": "Canvas-Skyward Integration API", "description": "API collection for Canvas LMS and Skyward SIS integration. Enables synchronization of grades, assignments, and enrollment data between Canvas and Skyward systems.\n\n## Features\n\n- OAuth2 authentication with token refresh\n    \n- Account and course management\n    \n- Enrollment tracking\n    \n- Assignment and grade synchronization\n    \n- Error handling and retry logic\n    \n- Rate limiting compliance\n    \n\n## Authentication\n\nThis collection uses OAuth2 Bearer token authentication. Set up your environment variables:\n\n- `canvas_base_url`: Your Canvas instance URL\n    \n- `client_id`: OAuth client ID\n    \n- `client_secret`: OAuth client secret\n    \n- `refresh_token`: OAuth refresh token\n    \n- `access_token`: Current access token (auto-refreshed)\n    \n\n## Environment Setup\n\nCreate a Postman environment with these variables:\n\n- canvas_base_url: [https://your-canvas-instance.instructure.com](https://your-canvas-instance.instructure.com)\n    \n- client_id: your_oauth_client_id\n    \n- client_secret: your_oauth_client_secret\n    \n- refresh_token: your_refresh_token\n    \n- access_token: (will be set automatically)\n    \n\n## Rate Limiting\n\n- 3000 requests per hour per token\n    \n- 100 requests per 10-second burst window\n    \n- Implement 50ms delays for optimal performance", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "42619635", "_collection_link": "https://mbernat-c3ff800a-7463507.postman.co/workspace/ea9f908c-8447-4db5-813d-ffcac45e250d/collection/42619635-881d51ab-5d9b-412f-8f97-bd53f63de90a?action=share&source=collection_link&creator=42619635"}, "item": [{"name": "Authentication", "item": [{"name": "Refresh OAuth2 Token", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["// Test for successful token refresh", "pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "// Extract and store new tokens", "if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('access_token', response.access_token);", "    pm.environment.set('refresh_token', response.refresh_token);", "    console.log('To<PERSON>s refreshed successfully');", "    ", "    pm.test('Access token received', function () {", "        pm.expect(response.access_token).to.be.a('string').and.not.empty;", "    });", "    ", "    pm.test('Token expires in reasonable time', function () {", "        pm.expect(response.expires_in).to.be.above(0);", "    });", "}"]}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "grant_type", "value": "refresh_token", "type": "text"}, {"key": "refresh_token", "value": "{{refresh_token}}", "type": "text"}, {"key": "client_id", "value": "{{client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{client_secret}}", "type": "text"}]}, "url": {"raw": "{{canvas_base_url}}/login/oauth2/token", "host": ["{{canvas_base_url}}"], "path": ["login", "oauth2", "token"]}, "description": "Refreshes the OAuth2 access token for Canvas API access. This endpoint should be called when the current access_token expires (HTTP 401 responses).\n\n**Required Parameters:**\n- grant_type: Must be \"refresh_token\"\n- refresh_token: The stored refresh token\n- client_id: Canvas OAuth client ID\n- client_secret: Canvas OAuth client secret\n\n**Response:**\n- access_token: New access token for API calls\n- refresh_token: New refresh token for future refreshes\n- expires_in: Token lifetime in seconds\n- token_type: Always \"Bearer\""}, "response": []}], "description": "OAuth2 authentication endpoints for Canvas API access"}, {"name": "Account Management", "item": [{"name": "Get Accounts", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response is an array', function () {", "    pm.expect(pm.response.json()).to.be.an('array');", "});", "", "if (pm.response.code === 200 && pm.response.json().length > 0) {", "    const account = pm.response.json()[0];", "    pm.environment.set('account_id', account.id);", "    ", "    pm.test('Account has required fields', function () {", "        pm.expect(account).to.have.property('id');", "        pm.expect(account).to.have.property('name');", "    });", "}"]}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{canvas_base_url}}/api/{{api_version}}/accounts", "host": ["{{canvas_base_url}}"], "path": ["api", "{{api_version}}", "accounts"]}, "description": "Retrieves list of Canvas accounts accessible to the authenticated user. This is typically used to identify the root account or specific sub-accounts for data synchronization.\n\n**Response Fields:**\n- id: Account identifier\n- name: Account display name\n- uuid: Unique identifier string\n- parent_account_id: Parent account (null for root)\n- root_account_id: Root account identifier\n\n**Usage Notes:**\n- First account is typically the root account\n- Account ID is required for subsequent API calls\n- Store account_id in environment for reuse"}, "response": []}, {"name": "Get Account Terms", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has enrollment_terms', function () {", "    pm.expect(pm.response.json()).to.have.property('enrollment_terms');", "    pm.expect(pm.response.json().enrollment_terms).to.be.an('array');", "});", "", "if (pm.response.code === 200) {", "    const terms = pm.response.json().enrollment_terms;", "    // Filter out archive terms and store active term", "    const activeTerms = terms.filter(term => !term.name.includes('X MASS ARCHIVE'));", "    if (activeTerms.length > 0) {", "        pm.environment.set('term_id', activeTerms[0].id);", "        console.log('Active terms found:', activeTerms.length);", "    }", "    ", "    pm.test('Terms have required fields', function () {", "        if (terms.length > 0) {", "            pm.expect(terms[0]).to.have.property('id');", "            pm.expect(terms[0]).to.have.property('name');", "        }", "    });", "}"]}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{canvas_base_url}}/api/{{api_version}}/accounts/{{account_id}}/terms", "host": ["{{canvas_base_url}}"], "path": ["api", "{{api_version}}", "accounts", "{{account_id}}", "terms"]}, "description": "Retrieves enrollment terms for a specific account. Terms are used to organize courses by semester, quarter, or other academic periods.\n\n**Path Parameters:**\n- account_id: The Canvas account ID (use {{account_id}} from environment)\n\n**Response Fields:**\n- enrollment_terms: Array of term objects\n  - id: Term identifier\n  - name: Term display name (e.g., \"Spring 2024 T3\")\n  - start_at: Term start date (ISO 8601)\n  - end_at: Term end date (ISO 8601)\n  - workflow_state: Term status (active, completed)\n\n**Processing Notes:**\n- Filters out terms containing \"X MASS ARCHIVE\"\n- Extract term codes (T1, T2, T3, T4) from term names\n- Maps to Skyward periods (1-4)"}, "response": []}, {"name": "Get Term Details", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "if (pm.response.code === 200) {", "    const term = pm.response.json();", "    ", "    pm.test('Term has required fields', function () {", "        pm.expect(term).to.have.property('id');", "        pm.expect(term).to.have.property('name');", "        pm.expect(term).to.have.property('workflow_state');", "    });", "    ", "    // Extract term code for Skyward mapping", "    const termMatch = term.name.match(/T([1-4])/);", "    if (termMatch) {", "        pm.environment.set('term_code', 'T' + termMatch[1]);", "        pm.environment.set('period', parseInt(termMatch[1]));", "        console.log('Term code extracted:', 'T' + termMatch[1]);", "    }", "}"]}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{canvas_base_url}}/api/{{api_version}}/accounts/1/terms/{{term_id}}", "host": ["{{canvas_base_url}}"], "path": ["api", "{{api_version}}", "accounts", "1", "terms", "{{term_id}}"]}, "description": "Retrieves detailed information about a specific enrollment term. Used to extract term codes and map to Skyward periods.\n\n**Path Parameters:**\n- term_id: The Canvas term ID (use {{term_id}} from environment)\n\n**Response Fields:**\n- id: Term identifier\n- name: Term display name\n- start_at: Term start date (ISO 8601)\n- end_at: Term end date (ISO 8601)\n- workflow_state: Term status\n\n**Skyward Mapping:**\n- Extracts T1-T4 codes from term name\n- Maps to periods 1-4 for Skyward SIS\n- Used in grade and assignment synchronization"}, "response": []}], "description": "Endpoints for managing Canvas accounts and terms"}, {"name": "Course Management", "item": [{"name": "Get Account Courses", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response is an array', function () {", "    pm.expect(pm.response.json()).to.be.an('array');", "});", "", "if (pm.response.code === 200 && pm.response.json().length > 0) {", "    const course = pm.response.json()[0];", "    pm.environment.set('course_id', course.id);", "    ", "    pm.test('Course has required fields', function () {", "        pm.expect(course).to.have.property('id');", "        pm.expect(course).to.have.property('name');", "        pm.expect(course).to.have.property('course_code');", "    });", "    ", "    console.log('Found', pm.response.json().length, 'courses for term');", "}"]}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{canvas_base_url}}/api/{{api_version}}/accounts/{{account_id}}/courses?enrollment_term_id={{term_id}}&state[]=available&state[]=completed&per_page=10000", "host": ["{{canvas_base_url}}"], "path": ["api", "{{api_version}}", "accounts", "{{account_id}}", "courses"], "query": [{"key": "enrollment_term_id", "value": "{{term_id}}", "description": "Filter courses by term ID"}, {"key": "state[]", "value": "available", "description": "Include available courses"}, {"key": "state[]", "value": "completed", "description": "Include completed courses"}, {"key": "per_page", "value": "10000", "description": "Maximum results per page (Canvas limit: 10000)"}]}, "description": "Retrieves courses for a specific account and enrollment term. This is the primary endpoint for discovering courses that need grade synchronization.\n\n**Path Parameters:**\n- account_id: The Canvas account ID\n\n**Query Parameters:**\n- enrollment_term_id: Filter courses by specific term\n- state[]: Array of course states (available, completed)\n- per_page: Results per page (max: 10000)\n\n**Response Fields:**\n- id: Course identifier\n- name: Course display name\n- course_code: Short course code\n- sis_course_id: SIS integration ID\n- enrollment_term_id: Associated term\n- workflow_state: Course status\n\n**Performance Notes:**\n- Process in chunks of 50 courses for memory efficiency\n- Use maximum page size for bulk operations\n- Cache course data to reduce API calls"}, "response": []}, {"name": "Get Course Sections", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response is an array', function () {", "    pm.expect(pm.response.json()).to.be.an('array');", "});", "", "if (pm.response.code === 200 && pm.response.json().length > 0) {", "    const section = pm.response.json()[0];", "    pm.environment.set('section_id', section.id);", "    ", "    pm.test('Section has required fields', function () {", "        pm.expect(section).to.have.property('id');", "        pm.expect(section).to.have.property('name');", "        pm.expect(section).to.have.property('course_id');", "    });", "    ", "    // Extract section code for Skyward mapping", "    if (section.sis_section_id) {", "        pm.environment.set('section_code', section.sis_section_id);", "    }", "}"]}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{canvas_base_url}}/api/{{api_version}}/courses/{{course_id}}/sections?per_page=10000", "host": ["{{canvas_base_url}}"], "path": ["api", "{{api_version}}", "courses", "{{course_id}}", "sections"], "query": [{"key": "per_page", "value": "10000", "description": "Maximum results per page"}]}, "description": "Retrieves sections for a specific course. Sections represent class periods or groups within a course and are essential for grade synchronization.\n\n**Path Parameters:**\n- course_id: The Canvas course ID\n\n**Query Parameters:**\n- per_page: Results per page (max: 10000)\n\n**Response Fields:**\n- id: Section identifier\n- name: Section display name\n- sis_section_id: SIS integration ID (maps to Skyward)\n- course_id: Parent course ID\n- nonxlist_course_id: Cross-listed course reference\n\n**Skyward Integration:**\n- sis_section_id maps to section_code in Skyward\n- Used for student enrollment tracking\n- Required for grade assignment to correct class periods"}, "response": []}], "description": "Endpoints for retrieving course and section information"}, {"name": "Enrollment Management", "item": [{"name": "Get Section Enrollments", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response is an array', function () {", "    pm.expect(pm.response.json()).to.be.an('array');", "});", "", "if (pm.response.code === 200 && pm.response.json().length > 0) {", "    const enrollment = pm.response.json()[0];", "    pm.environment.set('user_id', enrollment.user_id);", "    ", "    pm.test('Enrollment has required fields', function () {", "        pm.expect(enrollment).to.have.property('id');", "        pm.expect(enrollment).to.have.property('user_id');", "        pm.expect(enrollment).to.have.property('type');", "    });", "    ", "    if (enrollment.user && enrollment.user.integration_id) {", "        pm.test('Student ID starts with 32 (8 digits)', function () {", "            pm.expect(enrollment.user.integration_id).to.match(/^32\\d{6}$/);", "        });", "    }", "    ", "    console.log('Found', pm.response.json().length, 'student enrollments');", "}"]}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{canvas_base_url}}/api/{{api_version}}/sections/{{section_id}}/enrollments?type[]=StudentEnrollment&include[]=grades&include[]=user&enrollment_state=active&per_page=10000", "host": ["{{canvas_base_url}}"], "path": ["api", "{{api_version}}", "sections", "{{section_id}}", "enrollments"], "query": [{"key": "type[]", "value": "StudentEnrollment", "description": "Filter to student enrollments only"}, {"key": "include[]", "value": "grades", "description": "Include current grades"}, {"key": "include[]", "value": "user", "description": "Include user information"}, {"key": "enrollment_state", "value": "active", "description": "Active enrollments only"}, {"key": "per_page", "value": "10000", "description": "Maximum results per page"}, {"key": "grading_period_id", "value": "{{grading_period_id}}", "description": "Filter by grading period (optional)", "disabled": true}]}, "description": "Retrieves student enrollments for a specific section, including grade information. This is critical for grade synchronization with Skyward SIS.\n\n**Path Parameters:**\n- section_id: The Canvas section ID\n\n**Query Parameters:**\n- type[]: Filter by enrollment type (StudentEnrollment)\n- include[]: Additional data (grades, user)\n- enrollment_state: Filter by status (active)\n- grading_period_id: Optional grading period filter\n- per_page: Results per page (max: 10000)\n\n**Response Fields:**\n- id: Enrollment identifier\n- user_id: Student Canvas ID\n- course_id: Course identifier\n- type: Enrollment type\n- enrollment_state: Status\n- user: Student information\n  - id: Canvas user ID\n  - name: Student full name\n  - integration_id: SIS ID (8-digit, starts with 32)\n- grades: Current grade information\n  - current_grade: Letter grade\n  - current_score: Percentage score\n  - final_grade: Final letter grade\n  - final_score: Final percentage\n\n**Skyward Mapping:**\n- user.integration_id → student_id (must be 8 digits starting with 32)\n- grades.current_grade → letter_grade\n- grades.current_score → percentage"}, "response": []}, {"name": "Get Course Enrollments", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response is an array', function () {", "    pm.expect(pm.response.json()).to.be.an('array');", "});", "", "if (pm.response.code === 200) {", "    const enrollments = pm.response.json();", "    const students = enrollments.filter(e => e.type === 'StudentEnrollment');", "    const teachers = enrollments.filter(e => e.type === 'TeacherEnrollment');", "    ", "    console.log('Students:', students.length, 'Teachers:', teachers.length);", "    ", "    if (teachers.length > 0) {", "        pm.environment.set('teacher_id', teachers[0].user_id);", "        if (teachers[0].user) {", "            pm.environment.set('teacher_name', teachers[0].user.name);", "        }", "    }", "}"]}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{canvas_base_url}}/api/{{api_version}}/courses/{{course_id}}/enrollments?type[]=StudentEnrollment&type[]=TeacherEnrollment&include[]=user&enrollment_state=active&per_page=10000", "host": ["{{canvas_base_url}}"], "path": ["api", "{{api_version}}", "courses", "{{course_id}}", "enrollments"], "query": [{"key": "type[]", "value": "StudentEnrollment", "description": "Include student enrollments"}, {"key": "type[]", "value": "TeacherEnrollment", "description": "Include teacher enrollments"}, {"key": "include[]", "value": "user", "description": "Include user information"}, {"key": "enrollment_state", "value": "active", "description": "Active enrollments only"}, {"key": "per_page", "value": "10000", "description": "Maximum results per page"}]}, "description": "Retrieves all enrollments for a specific course, including both students and teachers. Used for comprehensive course roster information.\n\n**Path Parameters:**\n- course_id: The Canvas course ID\n\n**Query Parameters:**\n- type[]: Array of enrollment types (StudentEnrollment, TeacherEnrollment)\n- include[]: Additional data (user)\n- enrollment_state: Filter by status (active)\n- per_page: Results per page (max: 10000)\n\n**Teacher Information Mapping:**\n- user.name → teacher_name\n- user.integration_id → staff_id (for teachers)\n\n**Use Cases:**\n- Get complete course roster\n- Identify course instructors\n- Bulk enrollment processing\n- Teacher assignment to courses in Skyward"}, "response": []}], "description": "Endpoints for managing student and teacher enrollments"}, {"name": "Assignment Management", "item": [{"name": "Get Course Assignments", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response is an array', function () {", "    pm.expect(pm.response.json()).to.be.an('array');", "});", "", "if (pm.response.code === 200 && pm.response.json().length > 0) {", "    const assignment = pm.response.json()[0];", "    pm.environment.set('assignment_id', assignment.id);", "    ", "    pm.test('Assignment has required fields', function () {", "        pm.expect(assignment).to.have.property('id');", "        pm.expect(assignment).to.have.property('name');", "        pm.expect(assignment).to.have.property('points_possible');", "    });", "    ", "    console.log('Found', pm.response.json().length, 'assignments');", "}"]}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{canvas_base_url}}/api/{{api_version}}/courses/{{course_id}}/assignments?per_page=10000", "host": ["{{canvas_base_url}}"], "path": ["api", "{{api_version}}", "courses", "{{course_id}}", "assignments"], "query": [{"key": "per_page", "value": "10000", "description": "Maximum results per page"}]}, "description": "Retrieves assignments for a specific course. Essential for assignment-level grade synchronization with Skyward SIS.\n\n**Path Parameters:**\n- course_id: The Canvas course ID\n\n**Query Parameters:**\n- per_page: Results per page (max: 10000)\n\n**Response Fields:**\n- id: Assignment identifier\n- name: Assignment title\n- description: Assignment description\n- due_at: Due date (ISO 8601)\n- points_possible: Maximum points\n- assignment_group_id: Category/group ID\n- published: Publication status\n\n**Skyward Mapping:**\n- id → assignment identifier\n- name → title\n- description → description\n- due_at → due_date\n- points_possible → possible_points\n- published → published status"}, "response": []}, {"name": "Get Assignment Groups", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response is an array', function () {", "    pm.expect(pm.response.json()).to.be.an('array');", "});", "", "if (pm.response.code === 200 && pm.response.json().length > 0) {", "    const group = pm.response.json()[0];", "    pm.environment.set('assignment_group_id', group.id);", "    ", "    pm.test('Assignment group has required fields', function () {", "        pm.expect(group).to.have.property('id');", "        pm.expect(group).to.have.property('name');", "    });", "}"]}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{canvas_base_url}}/api/{{api_version}}/courses/{{course_id}}/assignment_groups?per_page=10000", "host": ["{{canvas_base_url}}"], "path": ["api", "{{api_version}}", "courses", "{{course_id}}", "assignment_groups"], "query": [{"key": "per_page", "value": "10000", "description": "Maximum results per page"}]}, "description": "Retrieves assignment categories/groups for a course. Used to classify assignments and calculate weighted grades.\n\n**Path Parameters:**\n- course_id: The Canvas course ID\n\n**Query Parameters:**\n- per_page: Results per page (max: 10000)\n\n**Response Fields:**\n- id: Assignment group identifier\n- name: Group display name\n- weight: Group weight percentage\n- group_weight: Weighted score contribution\n\n**Skyward Integration:**\n- Maps to assignment categories\n- Used for weight_percentage calculation\n- Enables proper grade categorization"}, "response": []}, {"name": "Get Student Submissions", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response is an array', function () {", "    pm.expect(pm.response.json()).to.be.an('array');", "});", "", "if (pm.response.code === 200 && pm.response.json().length > 0) {", "    const submission = pm.response.json()[0];", "    ", "    pm.test('Submission has required fields', function () {", "        pm.expect(submission).to.have.property('id');", "        pm.expect(submission).to.have.property('assignment_id');", "        pm.expect(submission).to.have.property('user_id');", "    });", "    ", "    if (submission.score !== null && submission.assignment) {", "        pm.test('Score is within possible points', function () {", "            pm.expect(submission.score).to.be.at.most(submission.assignment.points_possible);", "        });", "    }", "    ", "    console.log('Found', pm.response.json().length, 'submissions');", "}"]}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{canvas_base_url}}/api/{{api_version}}/courses/{{course_id}}/students/submissions?student_ids[]=all&include[]=assignment&per_page=10000", "host": ["{{canvas_base_url}}"], "path": ["api", "{{api_version}}", "courses", "{{course_id}}", "students", "submissions"], "query": [{"key": "student_ids[]", "value": "all", "description": "Include all students ('all' or specific IDs)"}, {"key": "include[]", "value": "assignment", "description": "Include assignment details"}, {"key": "per_page", "value": "10000", "description": "Maximum results per page"}]}, "description": "Retrieves all student submissions for a course. This is the primary endpoint for assignment-level grade data synchronization.\n\n**Path Parameters:**\n- course_id: The Canvas course ID\n\n**Query Parameters:**\n- student_ids[]: Array of student IDs or 'all'\n- include[]: Additional data (assignment)\n- per_page: Results per page (max: 10000)\n\n**Response Fields:**\n- id: Submission identifier\n- assignment_id: Associated assignment\n- user_id: Student Canvas ID\n- score: Points earned (null if ungraded)\n- grade: Letter grade or points\n- submission_comments: Array of feedback comments\n- assignment: Assignment details (when included)\n\n**Skyward Mapping:**\n- score → earned_points\n- assignment.points_possible → possible_points\n- submission_comments[0].comment → comment\n- Combines with assignment data for complete records\n\n**Performance Notes:**\n- Large courses may have 50,000+ submissions\n- Process in batches to avoid memory issues\n- Filter by grading period if needed"}, "response": []}, {"name": "Get Grading Periods", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has grading_periods', function () {", "    pm.expect(pm.response.json()).to.have.property('grading_periods');", "    pm.expect(pm.response.json().grading_periods).to.be.an('array');", "});", "", "if (pm.response.code === 200) {", "    const periods = pm.response.json().grading_periods;", "    if (periods.length > 0) {", "        pm.environment.set('grading_period_id', periods[0].id);", "        console.log('Found', periods.length, 'grading periods');", "    }", "}"]}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{canvas_base_url}}/api/{{api_version}}/courses/{{course_id}}/grading_periods", "host": ["{{canvas_base_url}}"], "path": ["api", "{{api_version}}", "courses", "{{course_id}}", "grading_periods"]}, "description": "Retrieves grading periods for a course. Used to filter grades and assignments by specific reporting periods.\n\n**Path Parameters:**\n- course_id: The Canvas course ID\n\n**Response Fields:**\n- grading_periods: Array of grading period objects\n  - id: Grading period identifier\n  - title: Period display name\n  - start_date: Period start date (ISO 8601)\n  - end_date: Period end date (ISO 8601)\n\n**Usage:**\n- Filter enrollment and submission requests by grading_period_id\n- Align with Skyward reporting periods\n- Enable period-specific grade synchronization"}, "response": []}], "description": "Endpoints for retrieving assignments, submissions, and grading information"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Global rate limiting - add 50ms delay between requests", "setTimeout(function(){}, 50);"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global error handling", "if (pm.response.code === 401) {", "    console.log('Authentication failed - token may need refresh');", "}", "", "if (pm.response.code === 429) {", "    console.log('Rate limit exceeded - implement backoff');", "}", "", "// Log response time for monitoring", "console.log('Response time: ' + pm.response.responseTime + 'ms');"]}}], "variable": [{"key": "canvas_base_url", "value": "https://canvas.instructure.com", "type": "string", "description": "Base URL for Canvas instance"}, {"key": "api_version", "value": "v1", "type": "string", "description": "Canvas API version"}]}