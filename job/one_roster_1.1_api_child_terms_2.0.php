<?php
/*
* Copyright 2016-2021 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/
require_once(dirname(__FILE__) . '/utils/functions.php');
require_once(dirname(__FILE__) . '/utils/logging.php');
require(dirname(__FILE__) . '/utils/OneRosterAPI.php');
use Google\Cloud\Storage\StorageClient;
// Designate private function
function _uploadToGCS($data, $type, $bucket, $currentDate, $siteID)
{
    try {
        if (empty($data)) {
            error_log("No data to upload for type: $type");
            return;
        }
        // Convert data to JSON
        $jsonEncoded = json_encode($data);
        if ($jsonEncoded === false) {
            error_log("Failed to encode JSON for type: $type");
            return;
        }
        // Upload to site-id folder
        $tempFile1 = tmpfile();
        fwrite($tempFile1, $jsonEncoded);
        rewind($tempFile1);
        $fileName = "Abre_OneRoster_1.1_API_ChildTerms_2.0_{$type}.json";
        $bucket->upload($tempFile1, [
            'name' => "$currentDate/site-id/$siteID/$fileName"
        ]);
        fclose($tempFile1);
        // Upload to filename folder using a separate file handle
        $tempFile2 = tmpfile();
        fwrite($tempFile2, $jsonEncoded);
        rewind($tempFile2);
        $folderName = "Abre_OneRoster_1.1_API_ChildTerms_2.0_{$type}";
        $bucket->upload($tempFile2, [
            'name' => "$currentDate/filename/$folderName/$folderName-{$siteID}.json"
        ]);
        fclose($tempFile2);
        error_log("Successfully uploaded $type data to GCS");
    } catch (Exception $e) {
        error_log("Error uploading $type data to GCS: " . $e->getMessage());
    }
}
function runJob($db, $siteID, $config)
{
    ignore_user_abort(true);
    set_time_limit(0);
    $cronName = 'Abre One Roster 1.1 - API Child Terms 2.0';
    try {
        $uuid = Logger::logCronStart($db, $siteID, $cronName);
        define("MAX_IMPORT_LIMIT", 250);
        $jobStartTime = (new DateTime("now", new DateTimeZone('UTC')))->format("Y-m-d H:i:s");
        $schoolYearID = getCurrentSchoolYearID($db);
        $currentEndingSchoolYear = getCurrentEndSchoolYear($db);
        $error = null;
        $skip = null;
        $separator = "\r\n";
        $schoolsCache = [];
        $gradingPeriods = [];
        $usersCache = [];
        $teacherCache = [];
        $classUniqueArray = [];
        $classCache = [];
        $courseCache = [];
        // Initialize Google Cloud Storage once
        $storage = new StorageClient(['projectId' => "abre-production"]);
        $bucketName = "prd-landing-zone";
        $bucket = $storage->bucket($bucketName);
        $currentDate = date("Ymd");
        // Initialize arrays to hold data for bucket upload
        $schoolsData = [];
        $academicSessionsData = [];
        $studentsData = [];
        $staffData = [];
        $coursesData = [];
        $classesData = [];
        $enrollmentsData = [];
        $api = OneRosterAPI::init(
            $config->oneRoster->service,
            $config->oneRoster->clientID,
            $config->oneRoster->clientSecret,
            $config->oneRoster->baseUri,
            $config->oneRoster->authUri
        );

        error_log("starting schools");
        $valuesToImport = [];
        $dbColumns = "";
        $schools = $api->get("ims/oneroster/v1p1/schools");
        if (isset($schools["orgs"]) && count($schools["orgs"])) {
            $dbColumns = "INSERT INTO abre_schools (code, name, site_id, school_year_id) VALUES ";

            foreach ($schools["orgs"] as $id => $school) {
                $code = $school["identifier"] != "" ? $school["identifier"] : $school["sourcedId"];
                $codeEscaped = trim($db->escape_string($code));

                $nameEscaped = trim($db->escape_string($school["name"]));

                $valuesToImport[] = "(
          '$codeEscaped', '$nameEscaped', $siteID, $schoolYearID
        )";

                if (count($valuesToImport) == MAX_IMPORT_LIMIT) {
                    insertRows($db, $dbColumns, $valuesToImport, "ON DUPLICATE KEY UPDATE abre_schools.code = VALUES(code), abre_schools.name = VALUES(name), abre_schools.site_id = VALUES(site_id)");
                    $valuesToImport = [];
                }

                $schoolsCache[$school["sourcedId"]] = [
                    "id" => $code,
                    "name" => $school["name"],
                    "type" => $school["type"]
                ];

                // Add schools to data collection
                $schoolsData[] = $school;
            }
            if (count($valuesToImport)) {
                insertRows($db, $dbColumns, $valuesToImport, "ON DUPLICATE KEY UPDATE abre_schools.code = VALUES(code), abre_schools.name = VALUES(name), abre_schools.site_id = VALUES(site_id)");
            }
        }
        // Upload schools data to GCS
        if (!empty($schoolsData)) {
            _uploadToGCS($schoolsData, 'schools', $bucket, $currentDate, $siteID);
        }
        $schools = null;
        error_log("done with schools");

        error_log("starting academic sessions");
        $valuesToImport = [];
        $dbColumns = "";
        $academicSessions = $api->get("ims/oneroster/v1p1/academicSessions");
        if (isset($academicSessions["academicSessions"]) && count($academicSessions["academicSessions"])) {
            $deleteSql = "DELETE FROM abre_term WHERE site_id = ? AND term_year_id = ? AND is_imported = 1";
            $deleteStmt = $db->stmt_init();
            $deleteStmt->prepare($deleteSql);
            $deleteStmt->bind_param("ii", $siteID, $schoolYearID);
            $deleteStmt->execute();
            $deleteStmt->close();

            $termColumns = "INSERT INTO abre_term
                        (site_id, term_definition_id, term_year_id, start_date, end_date, is_imported)
                      VALUES ";
            $termDefinitions = [];
            foreach ($academicSessions["academicSessions"] as $id => $session) {
                // Add academic sessions to data collection
                $academicSessionsData[] = $session;

                // $session["schoolYear"] == $currentEndingSchoolYear
                //   &&
                if ($session["type"] != "schoolYear") {

                    $customSourceID = $session["sourcedId"] . "-$siteID";
                    if (!isset($termDefinitions[$customSourceID])) {
                        $existingDefinitionID = null;
                        $sessionSelectSql = "SELECT id FROM abre_term_definition WHERE term_input = ?";
                        $sessionSelectStmt = $db->stmt_init();
                        $sessionSelectStmt->prepare($sessionSelectSql);
                        $sessionSelectStmt->bind_param("s", $customSourceID);
                        $sessionSelectStmt->execute();
                        $sessionSelectStmt->bind_result($existingDefinitionID);
                        $sessionSelectStmt->fetch();
                        $sessionSelectStmt->close();

                        if ($existingDefinitionID === null) {
                            $insertTerm = "INSERT INTO abre_term_definition (term_input, display_short, display_long, is_hidden)
                              VALUES (?, ?, ?, 0)";
                            $insertStmt = $db->stmt_init();
                            $insertStmt->prepare($insertTerm);
                            $insertStmt->bind_param("sss", $customSourceID, $session["title"], $session["title"]);
                            $insertStmt->execute();
                            $newTermID = $insertStmt->insert_id;
                            $insertStmt->close();
                            $termDefinitions[$customSourceID] = $newTermID;
                        } else {
                            $termDefinitions[$customSourceID] = $existingDefinitionID;
                        }
                    }

                    $escapedTermID = trim($db->escape_string($termDefinitions[$customSourceID]));
                    $escapedStartDate = trim($db->escape_string($session["startDate"]));
                    $escapedEndDate = trim($db->escape_string($session["endDate"]));
                    $valuesToImport[] = "(
            $siteID, $escapedTermID, $schoolYearID, '$escapedStartDate', '$escapedEndDate', 1
          )";

                    if (count($valuesToImport) == MAX_IMPORT_LIMIT) {
                        insertRows($db, $termColumns, $valuesToImport);
                        $valuesToImport = [];
                    }

                    $gradingPeriods[$customSourceID] = [
                        "children" => $session["children"]
                    ];
                }
            }
            if (count($valuesToImport)) {
                insertRows($db, $termColumns, $valuesToImport);
            }
        }
        // Upload academic sessions data to GCS
        if (!empty($academicSessionsData)) {
            _uploadToGCS($academicSessionsData, 'academicSessions', $bucket, $currentDate, $siteID);
        }
        $academicSessions = null;
        error_log("done with academic sessions");
        error_log("starting students");
        $limit = 1000;
        $offset = 0;
        $deleteRecords = true;
        $valuesToImport = [];
        $emailsToImport = [];
        $dbColumns = "INSERT INTO Abre_Students
                  (StudentId, FirstName, MiddleName, LastName, Email, SSID, SchoolCode,
                    SchoolName, CurrentGrade, okay_to_publish, siteID, school_year_id)
                  VALUES ";
        $adColumns = "INSERT INTO Abre_AD (Email, StudentID, siteID, school_year_id) VALUES ";
        do {
            $students = $api->get("ims/oneroster/v1p1/students", [
                "offset" => $offset,
                "limit" => $limit
            ]);
            if (isset($students["users"]) && count($students["users"])) {
                if ($deleteRecords) {
                    $deleteSql = "DELETE FROM Abre_Students WHERE siteID = ? AND school_year_id = ?";
                    $deleteStmt = $db->stmt_init();
                    $deleteStmt->prepare($deleteSql);
                    $deleteStmt->bind_param("ii", $siteID, $schoolYearID);
                    $deleteStmt->execute();
                    $deleteStmt->close();
                    $deleteSql = "DELETE FROM Abre_AD WHERE siteID = ? AND school_year_id = ?";
                    $deleteStmt = $db->stmt_init();
                    $deleteStmt->prepare($deleteSql);
                    $deleteStmt->bind_param("ii", $siteID, $schoolYearID);
                    $deleteStmt->execute();
                    $deleteStmt->close();
                    $deleteRecords = false;
                }
                foreach ($students["users"] as $id => $student) {
                    if ($student["enabledUser"] && $student["status"] == "active") {
                        if (isset($config->oneRoster->studentId)) {
                            if ($config->oneRoster->studentId === 'sourcedId') {
                                $studentID = $student["sourcedId"];
                            } elseif ($config->oneRoster->studentId === 'identifier') {
                                $studentID = $student["identifier"];
                            } else {
                                // fallback in case it's set to something unexpected
                                $studentID = $student["identifier"] != "" ? $student["identifier"] : $student["sourcedId"];
                            }
                        } else {
                            $studentID = $student["identifier"] != "" ? $student["identifier"] : $student["sourcedId"];
                        }
                        $studentID = trim($db->escape_string($studentID));
                        $stateId = '';
                        if (isset($student['userIds'])) {
                            foreach ($student['userIds'] as $userId) {
                                if ($userId['type'] === 'stateId') {
                                    $stateId = trim($db->escape_string($userId['identifier']));
                                    break;
                                }
                            }
                        }
                        $firstName = trim($db->escape_string($student["givenName"]));
                        $middleName = trim($db->escape_string($student["middleName"]));
                        $lastName = trim($db->escape_string($student["familyName"]));
                        $email = trim($db->escape_string($student["email"]));
                        $currentGrade = trim($db->escape_string($student["grades"][0]));
                        $emailsToImport[] = "('$email', '$studentID', $siteID, $schoolYearID)";
                        if (count($emailsToImport) == MAX_IMPORT_LIMIT) {
                            insertRows($db, $adColumns, $emailsToImport);
                            $emailsToImport = [];
                        }

                        foreach ($student["orgs"] as $building) {
                            $schoolCode = $schoolsCache[$building["sourcedId"]]["id"];
                            $schoolCode = trim($db->escape_string($schoolCode));

                            $buildingName = trim($db->escape_string($schoolsCache[$building["sourcedId"]]["name"]));
                            $buildingType = trim($db->escape_string($schoolsCache[$building["sourcedId"]]["type"]));
                            //Check if Building a School and if so add it
                            if ($buildingType == "school") {
                                $valuesToImport[] = "(
                  '$studentID', '$firstName', '$middleName', '$lastName', '$email', '$stateId', '$schoolCode',
                  '$buildingName', '$currentGrade', 1, $siteID, $schoolYearID
                )";
                            }
                            if (count($valuesToImport) == MAX_IMPORT_LIMIT) {
                                insertRows($db, $dbColumns, $valuesToImport);
                                $valuesToImport = [];
                            }
                        }
                        $usersCache[$student["sourcedId"]] = [
                            "localId" => $studentID,
                            "firstName" => $student["givenName"],
                            "middleName" => $student["middleName"],
                            "lastName" => $student["familyName"]
                        ];
                        // Add students to data collection
                        $studentsData[] = $student;
                    }
                }
            }
            $offset += $limit;
        } while (isset($students["users"]) && count($students["users"]));
        if (count($valuesToImport)) {
            insertRows($db, $dbColumns, $valuesToImport);
        }
        if (count($emailsToImport)) {
            insertRows($db, $adColumns, $emailsToImport);
        }
        // Upload students data to GCS
        if (!empty($studentsData)) {
            _uploadToGCS($studentsData, 'students', $bucket, $currentDate, $siteID);
        }
        $students = null;
        error_log("done with students");
        error_log("starting staff");
        $limit = 1000;
        $offset = 0;
        $valuesToImport = [];
        $dbColumns = "INSERT INTO Abre_Staff
              (StaffID, FirstName, MiddleName, LastName, EMail1,
                is_imported, imported_on, siteID, school_year_id)
              VALUES ";
        $emptyStringEncrypted = encrypt("", $config->dbKey->iv, $config->dbKey->key);
        $insertSql = "INSERT INTO directory
                  (updatedtime, superadmin, admin, picture, firstname, lastname,
                    middlename, address, city, state, zip, email, phone, extension,
                    cellphone, ss, dob, gender, ethnicity, title, contract, classification,
                    location, grade, subject, doh, senioritydate, effectivedate, rategroup,
                    step, educationlevel, salary, hours, probationreportdate,
                    statebackgroundcheck, federalbackgroundcheck, stateeducatorid,
                    licensetype1, licenseissuedate1, licenseexpirationdate1, licenseterm1,
                    licensetype2, licenseissuedate2, licenseexpirationdate2, licenseterm2,
                    licensetype3, licenseissuedate3, licenseexpirationdate3, licenseterm3,
                    licensetype4, licenseissuedate4, licenseexpirationdate4, licenseterm4,
                    licensetype5, licenseissuedate5, licenseexpirationdate5, licenseterm5,
                    licensetype6, licenseissuedate6, licenseexpirationdate6, licenseterm6,
                    permissions, role, contractdays, siteID
                  )
                  VALUES (CURRENT_TIMESTAMP, 0, 0, '', ?, ?, '', ?, ?, ?, ?, ?, ?, '',
                    ?, ?, ?, ?, ?, '', ?, '', '', '', '', ?, ?, ?, ?, ?, ?, ?, ?,
                    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
                    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
                  );";
        $insertStmt = $db->stmt_init();
        $insertStmt->prepare($insertSql);
        do {
            $users = $api->get("ims/oneroster/v1p1/users", [
                "offset" => $offset,
                "limit" => $limit
            ]);
            if (isset($users["users"]) && count($users["users"])) {
                foreach ($users["users"] as $id => $user) {
                    if ($user["role"] == "teacher") {
                        $staffID = $user["identifier"] != "" ? $user["identifier"] : $user["sourcedId"];
                        $escapedStaffID = trim($db->escape_string($staffID));
                        $firstName = trim($db->escape_string($user["givenName"]));
                        $middleName = trim($db->escape_string($user["middleName"]));
                        $lastName = trim($db->escape_string($user["familyName"]));
                        $email = trim($db->escape_string($user["email"]));
                        $valuesToImport[] = "(
              '$escapedStaffID', '$firstName', '$middleName', '$lastName', '$email', 1, NOW(), $siteID, $schoolYearID
            )";
                        if (count($valuesToImport) == MAX_IMPORT_LIMIT) {
                            $values = implode(",", $valuesToImport);
                            $importQuery = "$dbColumns $values ON DUPLICATE KEY UPDATE
                              FirstName = VALUES(FirstName),
                              MiddleName = VALUES(MiddleName),
                              LastName = VALUES(LastName),
                              EMail1 = VALUES(EMail1),
                              is_imported = 1,
                              imported_on = NOW(),
                              is_archived = 0";
                            $db->query($importQuery);
                            $valuesToImport = [];
                        }
                        $quotedSchools = array_map(function ($school) use ($schoolsCache) {
                            $schoolID = $schoolsCache[$school["sourcedId"]]["id"];
                            return "'$schoolID'";
                        }, $user["orgs"]);
                        $schoolsForSql = "(" . join(",", $quotedSchools) . ")";
                        $schoolDeleteSql = "DELETE FROM abre_staff_schools
                                WHERE school_code NOT IN $schoolsForSql
                                  AND is_imported = 1 AND staff_id = ? AND site_id = ?";
                        $deleteSchoolsStmt = $db->stmt_init();
                        $deleteSchoolsStmt->prepare($schoolDeleteSql);
                        $deleteSchoolsStmt->bind_param("si", $staffID, $siteID);
                        $deleteSchoolsStmt->execute();
                        $deleteSchoolsStmt->close();
                        $schoolsToImport = [];
                        foreach ($user["orgs"] as $school) {
                            $schoolID = $schoolsCache[$school["sourcedId"]]["id"];
                            $schoolsToImport[] = "('$escapedStaffID', '$schoolID', $siteID, 1, NOW())";
                        }
                        $schoolsImportString = implode(",", $schoolsToImport);
                        $schoolInsertSql = "INSERT INTO abre_staff_schools
                                  (staff_id, school_code, site_id, is_imported, imported_on)
                                VALUES $schoolsImportString
                                ON DUPLICATE KEY UPDATE
                                  is_imported = 1,
                                  imported_on = NOW()";
                        $insertSchoolStmt = $db->stmt_init();
                        $insertSchoolStmt->prepare($schoolInsertSql);
                        $insertSchoolStmt->execute();
                        $insertSchoolStmt->close();
                        if ($user["email"] != "") {
                            $selectSql = "SELECT COUNT(*) FROM directory WHERE email = ? AND siteID = ?";
                            $selectStmt = $db->stmt_init();
                            $selectStmt->prepare($selectSql);
                            $selectStmt->bind_param("si", $user["email"], $siteID);
                            $selectStmt->execute();
                            $selectStmt->bind_result($existingRow);
                            $selectStmt->fetch();
                            $selectStmt->close();
                            if (!$existingRow) {
                                $insertStmt->bind_param(
                                    "sssssssssssssssssssssssssssssssssssssssssssssssssssssi",
                                    $user["givenName"],
                                    $user["familyName"],
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $user["email"],
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $emptyStringEncrypted,
                                    $siteID
                                );
                                $insertStmt->execute();
                            }
                        }
                        $usersCache[$user["sourcedId"]] = [
                            "localId" => $staffID,
                            "firstName" => $user["givenName"],
                            "middleName" => $user["middleName"],
                            "lastName" => $user["familyName"]
                        ];
                        // Add staff to data collection
                        $staffData[] = $user;
                        $teacherClasses = $api->get("ims/oneroster/v1p1/teachers/" . $user["sourcedId"] . "/classes");
                        if ($teacherClasses !== false) {
                            foreach ($teacherClasses["classes"] as $class) {
                                $classUnique = $class["sourcedId"] . '-' . $staffID;
                                if (!isset($classUniqueArray[$classUnique])) {
                                    $classUniqueArray[$classUnique] = [
                                        "sourcedId" => $class["sourcedId"],
                                        "localId" => $staffID,
                                        "firstName" => $user["givenName"],
                                        "middleName" => $user["middleName"],
                                        "lastName" => $user["familyName"]
                                    ];
                                }
                                if (!isset($teacherCache[$class["sourcedId"]])) {
                                    $teacherCache[$class["sourcedId"]] = [
                                        "localId" => $staffID,
                                        "firstName" => $user["givenName"],
                                        "middleName" => $user["middleName"],
                                        "lastName" => $user["familyName"]
                                    ];
                                }
                            }
                        }
                    }
                }
            }
            $offset += $limit;
        } while (isset($users["users"]) && count($users["users"]));
        $insertStmt->close();
        if (count($valuesToImport)) {
            $values = implode(",", $valuesToImport);
            $importQuery = "$dbColumns $values ON DUPLICATE KEY UPDATE
                      FirstName = VALUES(FirstName),
                      MiddleName = VALUES(MiddleName),
                      LastName = VALUES(LastName),
                      EMail1 = VALUES(EMail1),
                      is_imported = 1,
                      imported_on = NOW(),
                      is_archived = 0";
            $db->query($importQuery);
            $valuesToImport = [];
        }
        //remove building relationships for staff no longer in import file
        $deleteOldBuildings = "DELETE FROM abre_staff_schools
                           WHERE site_id = ? AND is_imported = 1
                             AND imported_on < ?";
        $deleteOldStmt = $db->stmt_init();
        $deleteOldStmt->prepare($deleteOldBuildings);
        $deleteOldStmt->bind_param("is", $siteID, $jobStartTime);
        $deleteOldStmt->execute();
        $deleteOldStmt->close();
        //archive staff that are no longer in the import file
        $archiveOldStaff = "UPDATE Abre_Staff SET is_archived = 1
                        WHERE siteID = ? AND is_imported = 1
                          AND imported_on < ? AND school_year_id = ?";
        $archiveStmt = $db->stmt_init();
        $archiveStmt->prepare($archiveOldStaff);
        $archiveStmt->bind_param("isi", $siteID, $jobStartTime, $schoolYearID);
        $archiveStmt->execute();
        $archiveStmt->close();
        //ensure the archive flags in the staff table are consistent with the import
        //flags in the directory
        $directoryArchiveUpdate = "UPDATE directory d
                                JOIN (
                                  SELECT EMail1, siteID, MIN(is_archived) isArchived
                                    FROM Abre_Staff
                                  WHERE siteID = ? AND is_imported = 1 AND school_year_id = ?
                                  GROUP BY EMail1, siteID
                                ) staff
                                ON staff.EMail1 = d.email AND staff.siteID = d.siteID
                                SET d.archived = staff.isArchived";
        $directoryArchiveStmt = $db->stmt_init();
        $directoryArchiveStmt->prepare($directoryArchiveUpdate);
        $directoryArchiveStmt->bind_param("ii", $siteID, $schoolYearID);
        $directoryArchiveStmt->execute();
        $directoryArchiveStmt->close();
        // Upload staff data to GCS
        if (!empty($staffData)) {
            _uploadToGCS($staffData, 'staff', $bucket, $currentDate, $siteID);
        }
        $users = null;
        error_log("done with staff");
        error_log("starting courses");
        $limit = 1000;
        $offset = 0;
        do {
            $courses = $api->get("ims/oneroster/v1p1/courses", [
                "offset" => $offset,
                "limit" => $limit
            ]);
            if (isset($courses["courses"]) && count($courses["courses"])) {
                foreach ($courses["courses"] as $course) {
                    $courseID = $course["courseCode"] != "" ? $course["courseCode"] : $course["sourcedId"];
                    $courseCache[$course["sourcedId"]] = [
                        "id" => $courseID
                    ];
                    // Add courses to data collection
                    $coursesData[] = $course;
                }
            }
            $offset += $limit;
        } while (isset($courses["courses"]) && count($courses["courses"]));
        // Upload courses data to GCS
        if (!empty($coursesData)) {
            _uploadToGCS($coursesData, 'courses', $bucket, $currentDate, $siteID);
        }
        error_log("done with courses");
        error_log("starting classes");
        $limit = 1000;
        $offset = 0;
        $deleteRecords = true;
        $valuesToImport = [];
        $dbColumns = "INSERT INTO Abre_Courses
                    (SchoolCode, CourseCode, SectionCode, StaffID, TermCode, Period, siteID, school_year_id)
                  VALUES ";
        do {
            $classes = $api->get("ims/oneroster/v1p1/classes", [
                "offset" => $offset,
                "limit" => $limit
            ]);
            if (isset($classes["classes"]) && count($classes["classes"])) {
                if ($deleteRecords) {
                    $deleteSql = "DELETE FROM Abre_Courses WHERE siteID = ? AND school_year_id = ?";
                    $deleteStmt = $db->stmt_init();
                    $deleteStmt->prepare($deleteSql);
                    $deleteStmt->bind_param("ii", $siteID, $schoolYearID);
                    $deleteStmt->execute();
                    $deleteStmt->close();
                    $deleteRecords = false;
                }
                foreach ($classes["classes"] as $class) {
                    $classOrg = $class["school"]["sourcedId"];
                    $classOrg = trim($db->escape_string($classOrg));
                    $schoolCode = $schoolsCache[$classOrg]["id"];
                    $schoolCode = trim($db->escape_string($schoolCode));
                    $sectionCode = $class["sourcedId"];
                    $sectionCode = trim($db->escape_string($sectionCode));
                    if (isset($class["section_alias"])) {
                        $courseTitle = trim($db->escape_string($class["section_alias"]));
                    } else {
                        $courseTitle = trim($db->escape_string($class["title"]));
                    }
                    $courseCode = "";
                    if (!isset($courseCache[$class["course"]["sourcedId"]])) {
                        $course = $api->get("ims/oneroster/v1p1/courses/" . $class["course"]["sourcedId"]);
                        if ($course !== false) {
                            $courseCode = $course["course"]["courseCode"] != "" ? $course["course"]["courseCode"] : $course["course"]["sourcedId"];
                            $courseCache[$class["course"]["sourcedId"]] = [
                                "id" => $courseCode
                            ];
                            $courseCode = trim($db->escape_string($courseCode));
                        }
                    } else {
                        $courseCode = trim($db->escape_string($courseCache[$class["course"]["sourcedId"]]["id"]));
                    }
                    if (!isset($classCache[$class["sourcedId"]])) {
                        $classCache[$class["sourcedId"]] = [
                            "terms" => $class["terms"],
                            "periods" => $class["periods"],
                            "sectionCode" => $sectionCode,
                            "courseCode" => $courseCode,
                            "title" => $courseTitle
                        ];
                    }
                    $teacher = [];
                    if (isset($teacherCache[$class["sourcedId"]])) {
                        $teacher = $teacherCache[$class["sourcedId"]];
                    }
                    foreach ($class["terms"] as $term) {
                        foreach ($class["periods"] as $period) {
                            $escapedPeriod = trim($db->escape_string($period));
                            $teacherID = isset($teacher["localId"]) ? trim($db->escape_string($teacher["localId"])) : "";
                            $cachedTerms = $gradingPeriods[$term["sourcedId"] . "-$siteID"];
                            if (count($cachedTerms["children"])) {
                                foreach ($cachedTerms["children"] as $childTerm) {
                                    $escapedTerm = trim($db->escape_string($childTerm["sourcedId"] . "-$siteID"));
                                    $valuesToImport[] = "(
                    '$schoolCode', '$courseCode', '$sectionCode', '$teacherID',
                    '$escapedTerm', '$escapedPeriod', $siteID, $schoolYearID
                  )";
                                }
                            } else {
                                $escapedTerm = trim($db->escape_string($term["sourcedId"] . "-$siteID"));
                                $valuesToImport[] = "(
                  '$schoolCode', '$courseCode', '$sectionCode', '$teacherID',
                  '$escapedTerm', '$escapedPeriod', $siteID, $schoolYearID
                )";
                            }
                            if (count($valuesToImport) >= MAX_IMPORT_LIMIT) {
                                insertRows($db, $dbColumns, $valuesToImport);
                                $valuesToImport = [];
                            }
                        }
                    }
                    // Add classes to data collection
                    $classesData[] = $class;
                }
            }
            $offset += $limit;
        } while (isset($classes["classes"]) && count($classes["classes"]));
        if (count($valuesToImport)) {
            insertRows($db, $dbColumns, $valuesToImport);
        }
        // Upload classes data to GCS
        if (!empty($classesData)) {
            _uploadToGCS($classesData, 'classes', $bucket, $currentDate, $siteID);
        }
        $classes = null;
        error_log("done with classes");
        error_log("starting enrollments");
        $limit = 1000;
        $offset = 0;
        $deleteRecords = true;
        $valuesToImport = [
            "staff" => [],
            "student" => []
        ];
        $staffDbColumns = "INSERT INTO Abre_StaffSchedules
                      (StaffID, SchoolCode, CourseCode, SectionCode, TermCode, Period,
                        CourseName, TeacherName, siteID, school_year_id)
                      VALUES ";
        $studentDbColumns = "INSERT INTO Abre_StudentSchedules
                        (StudentID, FirstName, LastName, SchoolCode, CourseCode, SectionCode,
                          CourseName, StaffId, TeacherName, TermCode, Period, siteID, school_year_id)
                        VALUES ";
        do {
            $enrollments = $api->get("ims/oneroster/v1p1/enrollments", [
                "offset" => $offset,
                "limit" => $limit
            ]);
            if (isset($enrollments["enrollments"]) && count($enrollments["enrollments"])) {
                if ($deleteRecords) {
                    $deleteSql = "DELETE FROM Abre_StaffSchedules WHERE siteID = ? AND school_year_id = ?";
                    $deleteStmt = $db->stmt_init();
                    $deleteStmt->prepare($deleteSql);
                    $deleteStmt->bind_param("ii", $siteID, $schoolYearID);
                    $deleteStmt->execute();
                    $deleteStmt->close();
                    $deleteSql = "DELETE FROM Abre_StudentSchedules WHERE siteID = ? AND school_year_id = ?";
                    $deleteStmt = $db->stmt_init();
                    $deleteStmt->prepare($deleteSql);
                    $deleteStmt->bind_param("ii", $siteID, $schoolYearID);
                    $deleteStmt->execute();
                    $deleteStmt->close();
                    $deleteRecords = false;
                }
                foreach ($enrollments["enrollments"] as $id => $enrollmentRecord) {
                    $userSourcedId = $enrollmentRecord["user"]["sourcedId"];
                    $classSourcedId = $enrollmentRecord["class"]["sourcedId"];
                    $userID = "";
                    $firstName = "";
                    $lastName = "";
                    $fullName = "";
                    if (isset($usersCache[$userSourcedId])) {
                        $userID = trim($db->escape_string($usersCache[$userSourcedId]["localId"]));
                        $firstName = trim($db->escape_string($usersCache[$userSourcedId]["firstName"]));
                        $lastName = trim($db->escape_string($usersCache[$userSourcedId]["lastName"]));
                        $fullName = trim($db->escape_string("$firstName $lastName"));
                    } else {
                        continue;
                    }
                    $courseSourcedId = "";
                    $terms = [];
                    $periods = [];
                    $sectionCode = "";
                    $courseName = "";
                    $courseCode = "";
                    if (isset($classCache[$classSourcedId])) {
                        $terms = $classCache[$classSourcedId]["terms"];
                        $periods = $classCache[$classSourcedId]["periods"];
                        $sectionCode = trim($db->escape_string($classCache[$classSourcedId]["sectionCode"]));
                        $courseName = trim($db->escape_string($classCache[$classSourcedId]["title"]));
                        $courseCode = $classCache[$classSourcedId]["courseCode"];
                    } else {
                        $classRecord = $api->get("ims/oneroster/v1p1/classes/$classSourcedId");
                        if ($classRecord !== false) {
                            $terms = $classRecord["class"]["terms"];
                            $periods = $classRecord["class"]["periods"];
                            $sectionCode = trim($db->escape_string($classRecord["class"]["sourcedId"]));
                            if (isset($classRecord["class"]["section_alias"])) {
                                $courseName = trim($db->escape_string($classRecord["class"]["section_alias"]));
                            } else {
                                $courseName = trim($db->escape_string($classRecord["class"]["title"]));
                            }
                            $courseSourcedId = trim($db->escape_string($classRecord["class"]["course"]["sourcedId"]));
                        }
                        if ($courseSourcedId != "") {
                            $courseCode = "";
                            $courseRecord = $api->get("ims/oneroster/v1p1/courses/$courseSourcedId");
                            if ($courseRecord !== null) {
                                $courseCode = $courseRecord["course"]["courseCode"] != "" ? $courseRecord["course"]["courseCode"] : $courseRecord["course"]["sourcedId"];
                                $courseCode = trim($db->escape_string($courseCode));
                            }
                        }
                    }
                    $schoolCode = trim($db->escape_string($schoolsCache[$enrollmentRecord["school"]["sourcedId"]]["id"]));
                    if ($enrollmentRecord["role"] == "teacher" || $enrollmentRecord["role"] == "administrator") {
                        foreach ($terms as $term) {
                            foreach ($periods as $period) {
                                $escapedPeriod = trim($db->escape_string($period));
                                $cachedTerms = $gradingPeriods[$term["sourcedId"] . "-$siteID"];
                                if (count($cachedTerms["children"])) {
                                    foreach ($cachedTerms["children"] as $childTerm) {
                                        $escapedTermCode = trim($db->escape_string($childTerm["sourcedId"] . "-$siteID"));
                                        $valuesToImport["staff"][] = "(
                      '$userID', '$schoolCode', '$courseCode', '$sectionCode', '$escapedTermCode',
                      '$escapedPeriod', '$courseName', '$fullName', $siteID, $schoolYearID
                    )";
                                    }
                                } else {
                                    $escapedTermCode = trim($db->escape_string($term["sourcedId"] . "-$siteID"));
                                    $valuesToImport["staff"][] = "(
                    '$userID', '$schoolCode', '$courseCode', '$sectionCode', '$escapedTermCode',
                    '$escapedPeriod', '$courseName', '$fullName', $siteID, $schoolYearID
                  )";
                                }
                                if (count($valuesToImport["staff"]) >= MAX_IMPORT_LIMIT) {
                                    insertRows($db, $staffDbColumns, $valuesToImport["staff"]);
                                    $valuesToImport["staff"] = [];
                                }
                            }
                        }
                    } elseif ($enrollmentRecord["role"] == "student") {
                        //Loop Through All Unique Classes (Primary and Secondary Teachers)
                        $staffID = "";
                        $teacherName = "";
                        foreach ($classUniqueArray as $key => $value) {
                            if (strpos($key, $classSourcedId) !== false) {
                                $staffID = $value['localId'];
                                $teacherName = trim($db->escape_string($value["firstName"] . " " . $value["lastName"]));
                                foreach ($terms as $term) {
                                    foreach ($periods as $period) {
                                        $escapedPeriod = trim($db->escape_string($period));
                                        $cachedTerms = $gradingPeriods[$term["sourcedId"] . "-$siteID"];
                                        if (count($cachedTerms["children"])) {
                                            foreach ($cachedTerms["children"] as $childTerm) {
                                                $escapedTerm = trim($db->escape_string($childTerm["sourcedId"] . "-$siteID"));
                                                $valuesToImport["student"][] = "(
                              '$userID', '$firstName', '$lastName', '$schoolCode', '$courseCode', '$sectionCode',
                              '$courseName', '$staffID', '$teacherName', '$escapedTerm', '$escapedPeriod', $siteID, $schoolYearID
                            )";
                                            }
                                        } else {
                                            $escapedTerm = trim($db->escape_string($term["sourcedId"] . "-$siteID"));
                                            $valuesToImport["student"][] = "(
                            '$userID', '$firstName', '$lastName', '$schoolCode', '$courseCode', '$sectionCode',
                            '$courseName', '$staffID', '$teacherName', '$escapedTerm', '$escapedPeriod', $siteID, $schoolYearID
                          )";
                                        }
                                        if (count($valuesToImport["student"]) >= MAX_IMPORT_LIMIT) {
                                            insertRows($db, $studentDbColumns, $valuesToImport["student"]);
                                            $valuesToImport["student"] = [];
                                        }
                                    }
                                }
                            }
                        }
                    }
                    // Add enrollments to data collection
                    $enrollmentsData[] = $enrollmentRecord;
                }
                $offset += $limit;
            }
        } while (isset($enrollments["enrollments"]) && count($enrollments["enrollments"]));
        if (count($valuesToImport["staff"])) {
            insertRows($db, $staffDbColumns, $valuesToImport["staff"]);
        }
        if (count($valuesToImport["student"])) {
            insertRows($db, $studentDbColumns, $valuesToImport["student"]);
        }
        // Upload enrollments data to GCS
        if (!empty($enrollmentsData)) {
            _uploadToGCS($enrollmentsData, 'enrollments', $bucket, $currentDate, $siteID);
        }
        $enrollments = null;
        error_log("done with enrollments");
    } catch (Exception $ex) {
        $error = $ex->getMessage();
    }
    $details = [];
    if (isset($error) && !is_null($error)) {
        $details["error"] = $error;
        $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
    } else {
        $status = CRON_SUCCESS;
    }
    Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}
