# OS/Editor
.DS_Store
Thumbs.db
.idea/
.vscode/
WARP.md
.fleet/
.vs/
.history/
*.swp
*.swo
*~

# PHP / Composer
vendor/
composer.phar
composer.lock
.phpunit.result.cache
.php-cs-fixer.cache
.php_cs.cache
# Composer auth (tokens)
auth.json

# Node
**/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
.pnpm-debug.log*
.eslintcache

# Environment files
.env
.env.*
.env.local

# Logs and temp
logs/
*.log
*.tmp
tmp/
temp/
storage/

# Keys and credentials
ssh-keys/
*.pem
*.ppk
*.key

# Project-specific
test.php
